"""
واجهة المستخدم الرسومية المحسنة للإنقاذ والبحث
Enhanced Graphical User Interface for Search and Rescue
======================================================

واجهة رسومية متقدمة ومتخصصة لتطبيق معالجة وتصنيف صور الإنقاذ والبحث
مع دعم اللغة العربية والميزات التفاعلية المتقدمة.
"""

import os
import sys
import json
import threading
import queue
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    from tkinter import font as tkFont
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.patches as patches
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import seaborn as sns
    sns.set_style("whitegrid")
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

from ..utils.exceptions import ApplicationError


class ToolTip:
    """
    نظام التلميحات المحسن
    Enhanced tooltip system
    """

    def __init__(self, widget, text, delay=500, wraplength=250):
        """
        تهيئة التلميح

        Args:
            widget: العنصر المراد إضافة التلميح له
            text: نص التلميح
            delay: تأخير ظهور التلميح بالميلي ثانية
            wraplength: طول السطر للنص الطويل
        """
        self.widget = widget
        self.text = text
        self.delay = delay
        self.wraplength = wraplength
        self.tooltip_window = None
        self.id = None
        self.x = self.y = 0

        # ربط الأحداث
        self.widget.bind('<Enter>', self._on_enter)
        self.widget.bind('<Leave>', self._on_leave)
        self.widget.bind('<Motion>', self._on_motion)

    def _on_enter(self, event=None):
        """عند دخول الماوس للعنصر"""
        self._schedule_tooltip()

    def _on_leave(self, event=None):
        """عند مغادرة الماوس للعنصر"""
        self._cancel_tooltip()
        self._hide_tooltip()

    def _on_motion(self, event=None):
        """عند حركة الماوس"""
        self.x, self.y = event.x_root, event.y_root

    def _schedule_tooltip(self):
        """جدولة ظهور التلميح"""
        self._cancel_tooltip()
        self.id = self.widget.after(self.delay, self._show_tooltip)

    def _cancel_tooltip(self):
        """إلغاء جدولة التلميح"""
        if self.id:
            self.widget.after_cancel(self.id)
            self.id = None

    def _show_tooltip(self):
        """عرض التلميح"""
        if self.tooltip_window:
            return

        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)

        # تطبيق نمط التلميح
        self.tooltip_window.configure(bg='#2d3748', relief='solid', bd=1)

        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            font=('Arial', 9),
            bg='#2d3748',
            fg='#ffffff',
            padx=8,
            pady=4,
            wraplength=self.wraplength,
            justify=tk.LEFT
        )
        label.pack()

        # موضع التلميح
        x = self.x + 10
        y = self.y + 10

        # التأكد من أن التلميح داخل الشاشة
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()

        self.tooltip_window.update_idletasks()
        tooltip_width = self.tooltip_window.winfo_width()
        tooltip_height = self.tooltip_window.winfo_height()

        if x + tooltip_width > screen_width:
            x = self.x - tooltip_width - 10
        if y + tooltip_height > screen_height:
            y = self.y - tooltip_height - 10

        self.tooltip_window.geometry(f"+{x}+{y}")

    def _hide_tooltip(self):
        """إخفاء التلميح"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

    def update_text(self, new_text):
        """تحديث نص التلميح"""
        self.text = new_text


def add_tooltip(widget, text, delay=500, wraplength=250):
    """
    دالة مساعدة لإضافة تلميح لعنصر

    Args:
        widget: العنصر
        text: نص التلميح
        delay: تأخير الظهور
        wraplength: طول السطر

    Returns:
        ToolTip: كائن التلميح
    """
    return ToolTip(widget, text, delay, wraplength)


class DataVisualizationPanel:
    """
    لوحة تصور البيانات المتقدمة
    Advanced data visualization panel
    """

    def __init__(self, parent, theme=None):
        """
        تهيئة لوحة التصور

        Args:
            parent: الإطار الأب
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.current_data = None
        self.charts = {}

        self._setup_ui()

    def _setup_ui(self):
        """إعداد واجهة لوحة التصور"""
        # إطار رئيسي
        self.main_frame = ttk.LabelFrame(self.parent,
                                        text="📊 تصور البيانات والتحليلات",
                                        style='Card.TLabelFrame',
                                        padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط أدوات التصور
        self._create_visualization_toolbar()

        # منطقة الرسوم البيانية
        self._create_charts_area()

        # لوحة الإحصائيات
        self._create_statistics_panel()

    def _create_visualization_toolbar(self):
        """إنشاء شريط أدوات التصور"""
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # أزرار أنواع الرسوم البيانية
        chart_types = [
            ("📊 أعمدة", "bar", "رسم بياني بالأعمدة"),
            ("🥧 دائري", "pie", "رسم بياني دائري"),
            ("📈 خطي", "line", "رسم بياني خطي"),
            ("🗺️ خريطة حرارية", "heatmap", "خريطة حرارية"),
            ("📉 توزيع", "histogram", "رسم توزيع"),
            ("🎯 مبعثر", "scatter", "رسم مبعثر")
        ]

        for text, chart_type, tooltip in chart_types:
            btn = ttk.Button(toolbar_frame, text=text,
                           command=lambda ct=chart_type: self._create_chart(ct))
            btn.pack(side=tk.LEFT, padx=(0, 5))
            add_tooltip(btn, tooltip)

        # أزرار التحكم
        control_frame = ttk.Frame(toolbar_frame)
        control_frame.pack(side=tk.RIGHT)

        ttk.Button(control_frame, text="💾 حفظ",
                  command=self._save_charts).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_frame, text="🖨️ طباعة",
                  command=self._print_charts).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_frame, text="🗑️ مسح",
                  command=self._clear_charts).pack(side=tk.LEFT)

    def _create_charts_area(self):
        """إنشاء منطقة الرسوم البيانية"""
        # دفتر تبويبات للرسوم المختلفة
        self.charts_notebook = ttk.Notebook(self.main_frame)
        self.charts_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # تبويب الرسوم الأساسية
        self.basic_charts_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.basic_charts_frame, text="📊 الرسوم الأساسية")

        # تبويب التحليلات المتقدمة
        self.advanced_charts_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.advanced_charts_frame, text="📈 التحليلات المتقدمة")

        # تبويب المقارنات
        self.comparison_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.comparison_frame, text="⚖️ المقارنات")

        # إعداد منطقة الرسم لكل تبويب
        self._setup_chart_canvas(self.basic_charts_frame, "basic")
        self._setup_chart_canvas(self.advanced_charts_frame, "advanced")
        self._setup_chart_canvas(self.comparison_frame, "comparison")

    def _setup_chart_canvas(self, parent, chart_type):
        """إعداد منطقة الرسم"""
        if not MATPLOTLIB_AVAILABLE:
            tk.Label(parent, text="Matplotlib غير متوفر للرسوم البيانية",
                    font=self.theme.fonts['body'],
                    fg=self.theme.colors['danger']).pack(expand=True)
            return

        # إنشاء الشكل والمحاور
        fig = Figure(figsize=(12, 8), dpi=100, facecolor='white')

        # إعداد النمط
        if SEABORN_AVAILABLE:
            plt.style.use('seaborn-v0_8')

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # حفظ المراجع
        self.charts[chart_type] = {
            'figure': fig,
            'canvas': canvas,
            'axes': []
        }

    def _create_statistics_panel(self):
        """إنشاء لوحة الإحصائيات"""
        stats_frame = ttk.LabelFrame(self.main_frame,
                                    text="📈 الإحصائيات السريعة",
                                    style='Card.TLabelFrame',
                                    padding="10")
        stats_frame.pack(fill=tk.X)

        # إحصائيات في أعمدة
        self.stats_vars = {}
        stats_labels = [
            ("total_images", "إجمالي الصور", "0"),
            ("processed_images", "الصور المعالجة", "0"),
            ("success_rate", "معدل النجاح", "0%"),
            ("avg_processing_time", "متوسط وقت المعالجة", "0s"),
            ("detected_objects", "الأهداف المكتشفة", "0"),
            ("environments_found", "البيئات المكتشفة", "0")
        ]

        # تنظيم الإحصائيات في شبكة
        for i, (key, label, default) in enumerate(stats_labels):
            row = i // 3
            col = i % 3

            stat_frame = ttk.Frame(stats_frame)
            stat_frame.grid(row=row, column=col, padx=10, pady=5, sticky=tk.W)

            ttk.Label(stat_frame, text=f"{label}:",
                     style='Body.TLabel').pack(anchor=tk.W)

            self.stats_vars[key] = tk.StringVar(value=default)
            ttk.Label(stat_frame, textvariable=self.stats_vars[key],
                     style='Heading.TLabel').pack(anchor=tk.W)

    def update_data(self, data):
        """تحديث البيانات وإعادة رسم الرسوم البيانية"""
        self.current_data = data
        self._update_statistics()
        self._create_default_charts()

    def _update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        if not self.current_data:
            return

        # استخراج الإحصائيات من البيانات
        stats = self._extract_statistics(self.current_data)

        for key, value in stats.items():
            if key in self.stats_vars:
                self.stats_vars[key].set(str(value))

    def _extract_statistics(self, data):
        """استخراج الإحصائيات من البيانات"""
        stats = {}

        # إحصائيات أساسية
        stats['total_images'] = data.get('total_images', 0)
        stats['processed_images'] = data.get('processed_images', 0)

        # حساب معدل النجاح
        if stats['total_images'] > 0:
            success_rate = (stats['processed_images'] / stats['total_images']) * 100
            stats['success_rate'] = f"{success_rate:.1f}%"
        else:
            stats['success_rate'] = "0%"

        # متوسط وقت المعالجة
        processing_time = data.get('processing_time', 0)
        if stats['processed_images'] > 0:
            avg_time = processing_time / stats['processed_images']
            stats['avg_processing_time'] = f"{avg_time:.2f}s"
        else:
            stats['avg_processing_time'] = "0s"

        # إحصائيات التحليل
        analysis_results = data.get('analysis_results', {})
        stats['detected_objects'] = analysis_results.get('total_objects', 0)

        # عدد البيئات المكتشفة
        env_analysis = analysis_results.get('environment_analysis', {})
        env_distribution = env_analysis.get('distribution_percentages', {})
        stats['environments_found'] = len([env for env, pct in env_distribution.items() if pct > 0])

        return stats

    def _create_default_charts(self):
        """إنشاء الرسوم البيانية الافتراضية"""
        if not self.current_data or not MATPLOTLIB_AVAILABLE:
            return

        # رسم توزيع البيئات
        self._create_environment_distribution_chart()

        # رسم تقدم المعالجة
        self._create_processing_progress_chart()

        # رسم إحصائيات الأهداف
        self._create_objects_statistics_chart()

    def _create_environment_distribution_chart(self):
        """رسم توزيع البيئات"""
        analysis_results = self.current_data.get('analysis_results', {})
        env_analysis = analysis_results.get('environment_analysis', {})
        distribution = env_analysis.get('distribution_percentages', {})

        if not distribution:
            return

        # تنظيف البيانات
        environments = list(distribution.keys())
        percentages = list(distribution.values())

        # إنشاء الرسم البياني الدائري
        fig = self.charts['basic']['figure']
        fig.clear()

        ax = fig.add_subplot(221)  # 2x2 grid, position 1

        # ألوان مخصصة للبيئات
        colors = [
            self.theme.colors['sea'],
            self.theme.colors['desert'],
            self.theme.colors['coast'],
            self.theme.colors['urban'],
            self.theme.colors['forest'],
            self.theme.colors['mountain']
        ]

        wedges, texts, autotexts = ax.pie(percentages, labels=environments,
                                         autopct='%1.1f%%', colors=colors[:len(environments)],
                                         startangle=90)

        ax.set_title('توزيع البيئات المكتشفة', fontsize=14, fontweight='bold')

        # تحسين النص
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        self.charts['basic']['canvas'].draw()

    def _create_processing_progress_chart(self):
        """رسم تقدم المعالجة"""
        fig = self.charts['basic']['figure']
        ax = fig.add_subplot(222)  # 2x2 grid, position 2

        # بيانات وهمية للتقدم (يمكن استبدالها ببيانات حقيقية)
        stages = ['التحضير', 'التحميل', 'المعالجة', 'التحليل', 'الحفظ']
        progress = [100, 100, 85, 60, 30]  # نسب الإنجاز

        bars = ax.barh(stages, progress, color=self.theme.colors['primary'])

        # إضافة النسب على الأعمدة
        for i, (bar, pct) in enumerate(zip(bars, progress)):
            ax.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2,
                   f'{pct}%', va='center', fontweight='bold')

        ax.set_xlim(0, 110)
        ax.set_title('تقدم مراحل المعالجة', fontsize=14, fontweight='bold')
        ax.set_xlabel('نسبة الإنجاز (%)')

        self.charts['basic']['canvas'].draw()

    def _create_objects_statistics_chart(self):
        """رسم إحصائيات الأهداف المكتشفة"""
        fig = self.charts['basic']['figure']
        ax = fig.add_subplot(223)  # 2x2 grid, position 3

        # بيانات وهمية للأهداف (يمكن استبدالها ببيانات حقيقية)
        objects = ['أشخاص', 'مركبات', 'مباني', 'حطام', 'أخرى']
        counts = [45, 23, 67, 12, 8]

        bars = ax.bar(objects, counts, color=[
            self.theme.colors['success'],
            self.theme.colors['info'],
            self.theme.colors['warning'],
            self.theme.colors['danger'],
            self.theme.colors['text_muted']
        ])

        # إضافة القيم على الأعمدة
        for bar, count in zip(bars, counts):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                   str(count), ha='center', va='bottom', fontweight='bold')

        ax.set_title('الأهداف المكتشفة', fontsize=14, fontweight='bold')
        ax.set_ylabel('العدد')

        # تدوير تسميات المحور السيني
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        self.charts['basic']['canvas'].draw()

    def _create_chart(self, chart_type):
        """إنشاء رسم بياني من نوع محدد"""
        if not MATPLOTLIB_AVAILABLE or not self.current_data:
            return

        # تحديد الإطار المناسب
        if chart_type in ['bar', 'pie', 'line']:
            target_frame = 'basic'
        elif chart_type in ['heatmap', 'histogram']:
            target_frame = 'advanced'
        else:
            target_frame = 'comparison'

        fig = self.charts[target_frame]['figure']
        fig.clear()

        if chart_type == 'heatmap':
            self._create_heatmap_chart(fig)
        elif chart_type == 'histogram':
            self._create_histogram_chart(fig)
        elif chart_type == 'scatter':
            self._create_scatter_chart(fig)

        self.charts[target_frame]['canvas'].draw()

    def _create_heatmap_chart(self, fig):
        """إنشاء خريطة حرارية"""
        ax = fig.add_subplot(111)

        # بيانات وهمية للخريطة الحرارية
        data = np.random.rand(10, 10)

        im = ax.imshow(data, cmap='YlOrRd', aspect='auto')
        ax.set_title('خريطة حرارية لكثافة الأهداف', fontsize=14, fontweight='bold')

        # إضافة شريط الألوان
        fig.colorbar(im, ax=ax, label='الكثافة')

    def _create_histogram_chart(self, fig):
        """إنشاء رسم توزيع"""
        ax = fig.add_subplot(111)

        # بيانات وهمية للتوزيع
        data = np.random.normal(100, 15, 1000)

        ax.hist(data, bins=30, alpha=0.7, color=self.theme.colors['primary'])
        ax.set_title('توزيع أحجام الصور', fontsize=14, fontweight='bold')
        ax.set_xlabel('الحجم (KB)')
        ax.set_ylabel('التكرار')

    def _create_scatter_chart(self, fig):
        """إنشاء رسم مبعثر"""
        ax = fig.add_subplot(111)

        # بيانات وهمية للرسم المبعثر
        x = np.random.rand(100) * 100
        y = np.random.rand(100) * 100
        colors = np.random.rand(100)

        scatter = ax.scatter(x, y, c=colors, alpha=0.6, cmap='viridis')
        ax.set_title('العلاقة بين حجم الصورة ووقت المعالجة', fontsize=14, fontweight='bold')
        ax.set_xlabel('حجم الصورة (KB)')
        ax.set_ylabel('وقت المعالجة (ثانية)')

        # إضافة شريط الألوان
        fig.colorbar(scatter, ax=ax, label='جودة الكشف')

    def _save_charts(self):
        """حفظ الرسوم البيانية"""
        if not MATPLOTLIB_AVAILABLE:
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ الرسوم البيانية",
            defaultextension=".png",
            filetypes=[
                ("PNG", "*.png"),
                ("PDF", "*.pdf"),
                ("SVG", "*.svg"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                # حفظ الرسم النشط
                current_tab = self.charts_notebook.index(self.charts_notebook.select())
                chart_names = ['basic', 'advanced', 'comparison']
                current_chart = chart_names[current_tab]

                self.charts[current_chart]['figure'].savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("نجح", f"تم حفظ الرسم البياني: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الرسم البياني: {e}")

    def _print_charts(self):
        """طباعة الرسوم البيانية"""
        messagebox.showinfo("طباعة", "ميزة الطباعة ستكون متوفرة قريباً")

    def _clear_charts(self):
        """مسح جميع الرسوم البيانية"""
        if not MATPLOTLIB_AVAILABLE:
            return

        for chart_info in self.charts.values():
            chart_info['figure'].clear()
            chart_info['canvas'].draw()

        # إعادة تعيين الإحصائيات
        for var in self.stats_vars.values():
            var.set("0")


class RescueGUITheme:
    """
    نظام الألوان والتصميم المحسن للواجهة
    Enhanced color scheme and design system for the interface
    """

    def __init__(self):
        """تهيئة نظام الألوان المحسن"""
        # ألوان أساسية محسنة للإنقاذ والبحث
        self.colors = {
            # ألوان رئيسية محسنة
            'primary': '#2563eb',      # أزرق حديث
            'primary_dark': '#1d4ed8', # أزرق داكن
            'primary_light': '#3b82f6', # أزرق فاتح
            'secondary': '#dc2626',    # أحمر للطوارئ
            'secondary_dark': '#b91c1c', # أحمر داكن
            'accent': '#7c3aed',       # بنفسجي للتمييز
            'success': '#059669',      # أخضر للنجاح
            'success_light': '#10b981', # أخضر فاتح
            'warning': '#d97706',      # برتقالي للتحذير
            'warning_light': '#f59e0b', # برتقالي فاتح
            'info': '#0891b2',         # أزرق فاتح للمعلومات
            'danger': '#dc2626',       # أحمر للخطر
            'danger_light': '#ef4444', # أحمر فاتح

            # ألوان الخلفية المحسنة
            'bg_primary': '#ffffff',   # خلفية بيضاء نقية
            'bg_secondary': '#f8fafc', # خلفية رمادية فاتحة
            'bg_tertiary': '#f1f5f9',  # خلفية رمادية
            'bg_dark': '#1e293b',      # خلفية داكنة
            'bg_card': '#ffffff',      # خلفية البطاقات
            'bg_hover': '#f1f5f9',     # خلفية التمرير
            'bg_selected': '#e0f2fe',  # خلفية التحديد

            # ألوان النص المحسنة
            'text_primary': '#0f172a',
            'text_secondary': '#475569',
            'text_muted': '#64748b',
            'text_light': '#ffffff',
            'text_inverse': '#f8fafc',

            # ألوان الحدود
            'border_light': '#e2e8f0',
            'border_medium': '#cbd5e1',
            'border_dark': '#94a3b8',

            # ألوان البيئات المحسنة
            'sea': '#0ea5e9',
            'sea_light': '#38bdf8',
            'desert': '#f59e0b',
            'desert_light': '#fbbf24',
            'coast': '#10b981',
            'coast_light': '#34d399',
            'urban': '#6b7280',
            'urban_light': '#9ca3af',
            'forest': '#22c55e',
            'forest_light': '#4ade80',
            'mountain': '#8b5cf6',
            'mountain_light': '#a78bfa'
        }

        # خطوط محسنة مع دعم أفضل للعربية
        self.fonts = {
            'title': ('Segoe UI', 18, 'bold'),
            'title_large': ('Segoe UI', 24, 'bold'),
            'heading': ('Segoe UI', 14, 'bold'),
            'heading_large': ('Segoe UI', 16, 'bold'),
            'body': ('Segoe UI', 11),
            'body_large': ('Segoe UI', 12),
            'small': ('Segoe UI', 9),
            'caption': ('Segoe UI', 8),
            'arabic': ('Tahoma', 12),
            'arabic_large': ('Tahoma', 14),
            'arabic_title': ('Tahoma', 16, 'bold'),
            'monospace': ('Consolas', 10)
        }

        # أحجام وتباعد محسن
        self.spacing = {
            'xs': 2,
            'sm': 4,
            'md': 8,
            'lg': 12,
            'xl': 16,
            'xxl': 24,
            'xxxl': 32
        }

        # أحجام الأيقونات
        self.icon_sizes = {
            'small': 16,
            'medium': 24,
            'large': 32,
            'xlarge': 48
        }

        # نصف قطر الحدود
        self.border_radius = {
            'small': 4,
            'medium': 8,
            'large': 12,
            'round': 50
        }

    def configure_ttk_styles(self):
        """تكوين أنماط ttk المحسنة"""
        style = ttk.Style()

        # تحديد نمط عام محسن
        style.theme_use('clam')  # استخدام نمط أساسي حديث

        # تكوين الأنماط المخصصة للتسميات
        style.configure('Title.TLabel',
                       font=self.fonts['title'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_primary'])

        style.configure('TitleLarge.TLabel',
                       font=self.fonts['title_large'],
                       foreground=self.colors['primary'],
                       background=self.colors['bg_primary'])

        style.configure('Heading.TLabel',
                       font=self.fonts['heading'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_primary'])

        style.configure('HeadingLarge.TLabel',
                       font=self.fonts['heading_large'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_primary'])

        style.configure('Body.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['text_secondary'],
                       background=self.colors['bg_primary'])

        style.configure('Success.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['success'],
                       background=self.colors['bg_primary'])

        style.configure('Warning.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['warning'],
                       background=self.colors['bg_primary'])

        style.configure('Danger.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['danger'],
                       background=self.colors['bg_primary'])

        style.configure('Info.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['info'],
                       background=self.colors['bg_primary'])

        style.configure('Muted.TLabel',
                       font=self.fonts['small'],
                       foreground=self.colors['text_muted'],
                       background=self.colors['bg_primary'])

        # أزرار محسنة مع ألوان وتأثيرات
        style.configure('Primary.TButton',
                       font=self.fonts['body'],
                       foreground=self.colors['text_light'],
                       background=self.colors['primary'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(self.spacing['lg'], self.spacing['md']))

        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_dark'])])

        style.configure('Success.TButton',
                       font=self.fonts['body'],
                       foreground=self.colors['text_light'],
                       background=self.colors['success'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(self.spacing['lg'], self.spacing['md']))

        style.map('Success.TButton',
                 background=[('active', self.colors['success_light']),
                           ('pressed', self.colors['success_light'])])

        style.configure('Danger.TButton',
                       font=self.fonts['body'],
                       foreground=self.colors['text_light'],
                       background=self.colors['danger'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(self.spacing['lg'], self.spacing['md']))

        style.map('Danger.TButton',
                 background=[('active', self.colors['danger_light']),
                           ('pressed', self.colors['danger_light'])])

        style.configure('Warning.TButton',
                       font=self.fonts['body'],
                       foreground=self.colors['text_light'],
                       background=self.colors['warning'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(self.spacing['lg'], self.spacing['md']))

        style.map('Warning.TButton',
                 background=[('active', self.colors['warning_light']),
                           ('pressed', self.colors['warning_light'])])

        # إطارات محسنة
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='flat',
                       borderwidth=1)

        style.configure('Sidebar.TFrame',
                       background=self.colors['bg_secondary'],
                       relief='flat')

        # مجموعات التسميات محسنة
        style.configure('Card.TLabelFrame',
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['heading'],
                       relief='flat',
                       borderwidth=1)

        style.configure('Card.TLabelFrame.Label',
                       background=self.colors['bg_card'],
                       foreground=self.colors['primary'],
                       font=self.fonts['heading'])

        # دفتر التبويبات محسن
        style.configure('TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=0)

        style.configure('TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['body'],
                       padding=(self.spacing['lg'], self.spacing['md']))

        style.map('TNotebook.Tab',
                 background=[('selected', self.colors['bg_primary']),
                           ('active', self.colors['bg_hover'])],
                 foreground=[('selected', self.colors['primary']),
                           ('active', self.colors['text_primary'])])

        # شريط التقدم محسن
        style.configure('TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['bg_secondary'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

        # حقول الإدخال محسنة
        style.configure('TEntry',
                       font=self.fonts['body'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_primary'],
                       borderwidth=1,
                       relief='solid')

        style.map('TEntry',
                 focuscolor=[('!focus', self.colors['border_light']),
                           ('focus', self.colors['primary'])])

        # القوائم المنسدلة محسنة
        style.configure('TCombobox',
                       font=self.fonts['body'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_primary'],
                       borderwidth=1,
                       relief='solid')

        # أشرطة التمرير محسنة
        style.configure('TScrollbar',
                       background=self.colors['bg_secondary'],
                       troughcolor=self.colors['bg_tertiary'],
                       borderwidth=0,
                       arrowcolor=self.colors['text_muted'])

    def get_color(self, color_name: str, fallback: str = '#000000') -> str:
        """الحصول على لون من النظام"""
        return self.colors.get(color_name, fallback)

    def get_font(self, font_name: str, fallback: tuple = ('Arial', 11)) -> tuple:
        """الحصول على خط من النظام"""
        return self.fonts.get(font_name, fallback)

    def get_spacing(self, size: str, fallback: int = 8) -> int:
        """الحصول على تباعد من النظام"""
        return self.spacing.get(size, fallback)

    def create_gradient_frame(self, parent, color1: str, color2: str) -> tk.Frame:
        """إنشاء إطار بتدرج لوني (محاكاة)"""
        frame = tk.Frame(parent, bg=self.get_color(color1))
        return frame

    def apply_card_style(self, widget) -> None:
        """تطبيق نمط البطاقة على عنصر"""
        if hasattr(widget, 'configure'):
            widget.configure(
                bg=self.colors['bg_card'],
                relief='flat',
                bd=1,
                highlightbackground=self.colors['border_light'],
                highlightthickness=1
            )

    def apply_hover_effect(self, widget, enter_color: str = None, leave_color: str = None) -> None:
        """تطبيق تأثير التمرير على عنصر"""
        enter_color = enter_color or self.colors['bg_hover']
        leave_color = leave_color or self.colors['bg_primary']

        def on_enter(event):
            widget.configure(bg=enter_color)

        def on_leave(event):
            widget.configure(bg=leave_color)

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)


class ProgressDialog:
    """
    نافذة تقدم العملية المحسنة
    Enhanced progress dialog window
    """

    def __init__(self, parent, title="جاري المعالجة...", theme=None):
        """
        تهيئة نافذة التقدم المحسنة

        Args:
            parent: النافذة الأب
            title: عنوان النافذة
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)

        # تطبيق نمط النافذة
        self.dialog.configure(bg=self.theme.colors['bg_primary'])

        # جعل النافذة في المقدمة ووسط الشاشة
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self._center_window()

        # متغيرات التقدم
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="جاري التحضير...")
        self.detail_var = tk.StringVar(value="")
        self.cancelled = False

        # إعداد الواجهة
        self._setup_ui()

    def _center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def _setup_ui(self):
        """إعداد واجهة نافذة التقدم المحسنة"""
        # إطار رئيسي مع حشو
        main_frame = tk.Frame(self.dialog, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # أيقونة ومعلومات العملية
        header_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # أيقونة العملية
        icon_label = tk.Label(header_frame,
                             text="⚙️",
                             font=('Arial', 24),
                             bg=self.theme.colors['bg_primary'],
                             fg=self.theme.colors['primary'])
        icon_label.pack(side=tk.LEFT, padx=(0, 15))

        # معلومات العملية
        info_frame = tk.Frame(header_frame, bg=self.theme.colors['bg_primary'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        title_label = tk.Label(info_frame,
                              text="معالجة الصور",
                              font=self.theme.fonts['heading'],
                              bg=self.theme.colors['bg_primary'],
                              fg=self.theme.colors['text_primary'])
        title_label.pack(anchor=tk.W)

        # حالة العملية
        self.status_label = tk.Label(info_frame,
                                    textvariable=self.status_var,
                                    font=self.theme.fonts['body'],
                                    bg=self.theme.colors['bg_primary'],
                                    fg=self.theme.colors['text_secondary'])
        self.status_label.pack(anchor=tk.W)

        # شريط التقدم
        progress_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           style='TProgressbar')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        # نسبة التقدم
        self.progress_label = tk.Label(progress_frame,
                                      text="0%",
                                      font=self.theme.fonts['small'],
                                      bg=self.theme.colors['bg_primary'],
                                      fg=self.theme.colors['text_muted'])
        self.progress_label.pack(anchor=tk.E)

        # تفاصيل العملية
        details_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        details_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        details_label = tk.Label(details_frame,
                                text="التفاصيل:",
                                font=self.theme.fonts['body'],
                                bg=self.theme.colors['bg_primary'],
                                fg=self.theme.colors['text_primary'])
        details_label.pack(anchor=tk.W)

        self.details_text = tk.Text(details_frame,
                                   height=4,
                                   wrap=tk.WORD,
                                   font=self.theme.fonts['small'],
                                   bg=self.theme.colors['bg_secondary'],
                                   fg=self.theme.colors['text_secondary'],
                                   relief='flat',
                                   bd=1)
        self.details_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X)

        self.cancel_button = ttk.Button(buttons_frame,
                                       text="إلغاء",
                                       command=self._cancel_operation,
                                       style='Danger.TButton')
        self.cancel_button.pack(side=tk.RIGHT)

        self.minimize_button = ttk.Button(buttons_frame,
                                         text="تصغير",
                                         command=self._minimize_dialog,
                                         style='Primary.TButton')
        self.minimize_button.pack(side=tk.RIGHT, padx=(0, 10))

    def update_progress(self, value: float, status: str = "", details: str = ""):
        """تحديث التقدم والحالة"""
        self.progress_var.set(value)
        self.progress_label.config(text=f"{value:.1f}%")

        if status:
            self.status_var.set(status)

        if details:
            self.details_text.insert(tk.END, f"{details}\n")
            self.details_text.see(tk.END)

        self.dialog.update_idletasks()

    def set_status(self, status: str):
        """تحديث حالة العملية"""
        self.status_var.set(status)
        self.dialog.update_idletasks()

    def add_detail(self, detail: str):
        """إضافة تفصيل جديد"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.details_text.insert(tk.END, f"[{timestamp}] {detail}\n")
        self.details_text.see(tk.END)
        self.dialog.update_idletasks()

    def _cancel_operation(self):
        """إلغاء العملية"""
        self.cancelled = True
        self.cancel_button.config(state=tk.DISABLED, text="جاري الإلغاء...")

    def _minimize_dialog(self):
        """تصغير النافذة"""
        self.dialog.iconify()

    def is_cancelled(self) -> bool:
        """فحص ما إذا تم إلغاء العملية"""
        return self.cancelled

    def close(self):
        """إغلاق النافذة"""
        try:
            self.dialog.destroy()
        except:
            pass




class ImagePreviewPanel:
    """
    لوحة معاينة الصور المحسنة
    Enhanced image preview panel
    """

    def __init__(self, parent, theme=None):
        """
        تهيئة لوحة المعاينة المحسنة

        Args:
            parent: الإطار الأب
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.current_image_path = None
        self.current_image = None
        self.zoom_factor = 1.0
        self.max_zoom = 5.0
        self.min_zoom = 0.1
        self.canvas = None
        self.scrollbar_v = None
        self.scrollbar_h = None

        self._setup_ui()

    def _setup_ui(self):
        """إعداد واجهة لوحة المعاينة المحسنة"""
        # إطار رئيسي مع نمط البطاقة
        self.main_frame = ttk.LabelFrame(self.parent,
                                        text="🖼️ معاينة الصور",
                                        style='Card.TLabelFrame',
                                        padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط أدوات علوي
        self._create_toolbar()

        # منطقة عرض الصورة مع إمكانية التمرير
        self._create_image_display()

        # لوحة معلومات الصورة
        self._create_info_panel()

    def _create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # أزرار الملفات
        file_frame = ttk.Frame(toolbar_frame)
        file_frame.pack(side=tk.LEFT)

        ttk.Button(file_frame, text="📁 تحديد صورة",
                  command=self._select_image,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(file_frame, text="🗑️ مسح",
                  command=self._clear_image,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 5))

        # أزرار التكبير والتصغير
        zoom_frame = ttk.Frame(toolbar_frame)
        zoom_frame.pack(side=tk.RIGHT)

        ttk.Button(zoom_frame, text="🔍+",
                  command=self._zoom_in,
                  width=4).pack(side=tk.LEFT, padx=(0, 2))

        ttk.Button(zoom_frame, text="🔍-",
                  command=self._zoom_out,
                  width=4).pack(side=tk.LEFT, padx=(0, 2))

        ttk.Button(zoom_frame, text="⚡ ملائم",
                  command=self._fit_to_window,
                  width=6).pack(side=tk.LEFT, padx=(0, 2))

        # عرض مستوى التكبير
        self.zoom_label = ttk.Label(zoom_frame,
                                   text="100%",
                                   style='Muted.TLabel')
        self.zoom_label.pack(side=tk.LEFT, padx=(5, 0))

    def _create_image_display(self):
        """إنشاء منطقة عرض الصورة مع التمرير"""
        # إطار للكانفاس وأشرطة التمرير
        canvas_frame = ttk.Frame(self.main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # الكانفاس لعرض الصورة
        self.canvas = tk.Canvas(canvas_frame,
                               bg=self.theme.colors['bg_secondary'],
                               highlightthickness=0)

        # أشرطة التمرير
        self.scrollbar_v = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.scrollbar_h = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)

        self.canvas.configure(yscrollcommand=self.scrollbar_v.set,
                             xscrollcommand=self.scrollbar_h.set)

        # تخطيط الكانفاس وأشرطة التمرير
        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # ربط أحداث الماوس للتكبير والسحب
        self.canvas.bind("<Button-1>", self._on_canvas_click)
        self.canvas.bind("<B1-Motion>", self._on_canvas_drag)
        self.canvas.bind("<MouseWheel>", self._on_mouse_wheel)
        self.canvas.bind("<Button-4>", self._on_mouse_wheel)
        self.canvas.bind("<Button-5>", self._on_mouse_wheel)
        self.canvas.bind("<Button-3>", self._show_context_menu)  # قائمة السياق

        # إعداد السحب والإفلات
        self._setup_drag_and_drop()

        # رسالة افتراضية
        self._show_placeholder()

    def _setup_drag_and_drop(self):
        """إعداد وظيفة السحب والإفلات"""
        # ربط أحداث السحب والإفلات
        self.canvas.bind("<Button1-Motion>", self._on_drag_motion)
        self.canvas.bind("<ButtonRelease-1>", self._on_drop)

        # محاولة استخدام tkinterdnd2 إذا كان متوفراً
        try:
            import tkinterdnd2 as tkdnd

            # تمكين السحب والإفلات للملفات
            self.canvas.drop_target_register(tkdnd.DND_FILES)
            self.canvas.dnd_bind('<<Drop>>', self._on_file_drop)
            self.canvas.dnd_bind('<<DragEnter>>', self._on_drag_enter)
            self.canvas.dnd_bind('<<DragLeave>>', self._on_drag_leave)

        except ImportError:
            # إذا لم يكن tkinterdnd2 متوفراً، استخدم الطريقة البديلة
            self._setup_basic_drag_drop()

    def _setup_basic_drag_drop(self):
        """إعداد السحب والإفلات الأساسي"""
        # ربط أحداث لوحة المفاتيح للصق
        self.canvas.bind("<Control-v>", self._paste_from_clipboard)
        self.canvas.focus_set()

    def _on_file_drop(self, event):
        """معالجة إفلات الملفات"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')  # إزالة الأقواس إذا وجدت
            if self._is_image_file(file_path):
                self.load_image(file_path)
                self._add_tooltip_message("تم تحميل الصورة بنجاح!")
            else:
                self._add_tooltip_message("نوع الملف غير مدعوم", "warning")

    def _on_drag_enter(self, event):
        """عند دخول السحب لمنطقة الإفلات"""
        self.canvas.configure(bg=self.theme.colors['bg_hover'])
        self._show_drop_indicator()

    def _on_drag_leave(self, event):
        """عند مغادرة السحب لمنطقة الإفلات"""
        self.canvas.configure(bg=self.theme.colors['bg_secondary'])
        self._hide_drop_indicator()

    def _show_drop_indicator(self):
        """عرض مؤشر منطقة الإفلات"""
        self.canvas.delete("drop_indicator")
        self.canvas.create_rectangle(
            10, 10,
            self.canvas.winfo_width() - 10,
            self.canvas.winfo_height() - 10,
            outline=self.theme.colors['primary'],
            width=3,
            dash=(10, 5),
            tags="drop_indicator"
        )
        self.canvas.create_text(
            self.canvas.winfo_width() // 2,
            self.canvas.winfo_height() // 2,
            text="🎯 أفلت الصورة هنا",
            font=self.theme.fonts['heading'],
            fill=self.theme.colors['primary'],
            tags="drop_indicator"
        )

    def _hide_drop_indicator(self):
        """إخفاء مؤشر منطقة الإفلات"""
        self.canvas.delete("drop_indicator")

    def _is_image_file(self, file_path):
        """فحص ما إذا كان الملف صورة"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp'}
        return Path(file_path).suffix.lower() in image_extensions

    def _paste_from_clipboard(self, event):
        """لصق صورة من الحافظة"""
        try:
            from PIL import ImageGrab
            image = ImageGrab.grabclipboard()
            if image:
                # حفظ الصورة مؤقتاً
                temp_path = Path.cwd() / "temp_clipboard_image.png"
                image.save(temp_path)
                self.load_image(str(temp_path))
                self._add_tooltip_message("تم لصق الصورة من الحافظة!")
        except Exception as e:
            self._add_tooltip_message(f"فشل في لصق الصورة: {e}", "error")

    def _show_context_menu(self, event):
        """عرض قائمة السياق"""
        context_menu = tk.Menu(self.canvas, tearoff=0)

        # خيارات عامة
        context_menu.add_command(label="📁 تحديد صورة", command=self._select_image)
        context_menu.add_command(label="📋 لصق من الحافظة", command=lambda: self._paste_from_clipboard(None))
        context_menu.add_separator()

        if self.current_image:
            # خيارات خاصة بالصورة
            context_menu.add_command(label="🔍+ تكبير", command=self._zoom_in)
            context_menu.add_command(label="🔍- تصغير", command=self._zoom_out)
            context_menu.add_command(label="⚡ ملائم للنافذة", command=self._fit_to_window)
            context_menu.add_separator()
            context_menu.add_command(label="💾 حفظ نسخة", command=self._save_image_copy)
            context_menu.add_command(label="📤 تصدير", command=self._export_image)
            context_menu.add_separator()
            context_menu.add_command(label="🗑️ مسح", command=self._clear_image)

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def _save_image_copy(self):
        """حفظ نسخة من الصورة"""
        if not self.current_image:
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ نسخة من الصورة",
            defaultextension=".png",
            filetypes=[
                ("PNG", "*.png"),
                ("JPEG", "*.jpg"),
                ("BMP", "*.bmp"),
                ("TIFF", "*.tiff"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                self.current_image.save(file_path)
                self._add_tooltip_message(f"تم حفظ الصورة: {Path(file_path).name}")
            except Exception as e:
                self._add_tooltip_message(f"فشل في حفظ الصورة: {e}", "error")

    def _export_image(self):
        """تصدير الصورة بإعدادات متقدمة"""
        if not self.current_image:
            return

        # نافذة خيارات التصدير
        export_window = tk.Toplevel(self.main_frame)
        export_window.title("تصدير الصورة")
        export_window.geometry("400x300")
        export_window.configure(bg=self.theme.colors['bg_primary'])

        # محتوى النافذة
        ttk.Label(export_window, text="خيارات التصدير", style='Heading.TLabel').pack(pady=10)

        # خيارات الجودة والحجم
        quality_frame = ttk.LabelFrame(export_window, text="الجودة والحجم", padding="10")
        quality_frame.pack(fill=tk.X, padx=20, pady=10)

        # المزيد من خيارات التصدير يمكن إضافتها هنا
        ttk.Button(export_window, text="تصدير",
                  command=lambda: self._perform_export(export_window)).pack(pady=10)

    def _perform_export(self, window):
        """تنفيذ عملية التصدير"""
        window.destroy()
        self._save_image_copy()  # استخدام الطريقة البسيطة حالياً

    def _add_tooltip_message(self, message, level="info"):
        """إضافة رسالة تلميح مؤقتة"""
        # إنشاء تلميح مؤقت
        tooltip = tk.Toplevel(self.canvas)
        tooltip.wm_overrideredirect(True)
        tooltip.configure(bg=self.theme.colors['bg_dark'])

        # تحديد لون الرسالة
        color_map = {
            "info": self.theme.colors['info'],
            "success": self.theme.colors['success'],
            "warning": self.theme.colors['warning'],
            "error": self.theme.colors['danger']
        }

        color = color_map.get(level, self.theme.colors['info'])

        label = tk.Label(tooltip,
                        text=message,
                        font=self.theme.fonts['small'],
                        bg=self.theme.colors['bg_dark'],
                        fg=color,
                        padx=10,
                        pady=5)
        label.pack()

        # موضع التلميح
        x = self.canvas.winfo_rootx() + 20
        y = self.canvas.winfo_rooty() + 20
        tooltip.geometry(f"+{x}+{y}")

        # إخفاء التلميح بعد 3 ثوان
        tooltip.after(3000, tooltip.destroy)

    def _create_info_panel(self):
        """إنشاء لوحة معلومات الصورة"""
        info_frame = ttk.LabelFrame(self.main_frame,
                                   text="📊 معلومات الصورة",
                                   style='Card.TLabelFrame',
                                   padding="10")
        info_frame.pack(fill=tk.X)

        # إطار للمعلومات الأساسية
        basic_info_frame = ttk.Frame(info_frame)
        basic_info_frame.pack(fill=tk.X, pady=(0, 5))

        # معلومات في أعمدة
        self.filename_var = tk.StringVar(value="لا توجد صورة محددة")
        self.size_var = tk.StringVar(value="")
        self.format_var = tk.StringVar(value="")
        self.filesize_var = tk.StringVar(value="")

        ttk.Label(basic_info_frame, text="الملف:", style='Body.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.filename_var, style='Muted.TLabel').grid(row=0, column=1, sticky=tk.W)

        ttk.Label(basic_info_frame, text="الأبعاد:", style='Body.TLabel').grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.size_var, style='Muted.TLabel').grid(row=1, column=1, sticky=tk.W)

        ttk.Label(basic_info_frame, text="التنسيق:", style='Body.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        ttk.Label(basic_info_frame, textvariable=self.format_var, style='Muted.TLabel').grid(row=0, column=3, sticky=tk.W)

        ttk.Label(basic_info_frame, text="الحجم:", style='Body.TLabel').grid(row=1, column=2, sticky=tk.W, padx=(20, 5))
        ttk.Label(basic_info_frame, textvariable=self.filesize_var, style='Muted.TLabel').grid(row=1, column=3, sticky=tk.W)

    def _show_placeholder(self):
        """عرض رسالة افتراضية"""
        self.canvas.delete("all")
        text = "🖼️\n\nاسحب صورة هنا أو اضغط 'تحديد صورة'\nلبدء المعاينة"
        self.canvas.create_text(
            self.canvas.winfo_width() // 2,
            self.canvas.winfo_height() // 2,
            text=text,
            font=self.theme.fonts['body'],
            fill=self.theme.colors['text_muted'],
            justify=tk.CENTER
        )

    def _select_image(self):
        """تحديد صورة للمعاينة"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[
                ("ملفات الصور", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif *.gif"),
                ("JPEG", "*.jpg *.jpeg"),
                ("PNG", "*.png"),
                ("BMP", "*.bmp"),
                ("TIFF", "*.tiff *.tif"),
                ("GIF", "*.gif"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.load_image(file_path)

    def _clear_image(self):
        """مسح الصورة الحالية"""
        self.current_image_path = None
        self.current_image = None
        self.zoom_factor = 1.0
        self.canvas.delete("all")
        self._show_placeholder()
        self._update_info_display()
        self._update_zoom_display()

    def _zoom_in(self):
        """تكبير الصورة"""
        if self.current_image and self.zoom_factor < self.max_zoom:
            self.zoom_factor *= 1.2
            self._update_image_display()
            self._update_zoom_display()

    def _zoom_out(self):
        """تصغير الصورة"""
        if self.current_image and self.zoom_factor > self.min_zoom:
            self.zoom_factor /= 1.2
            self._update_image_display()
            self._update_zoom_display()

    def _fit_to_window(self):
        """ملائمة الصورة للنافذة"""
        if not self.current_image:
            return

        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            return

        img_width, img_height = self.current_image.size

        # حساب عامل التكبير المناسب
        zoom_x = canvas_width / img_width
        zoom_y = canvas_height / img_height
        self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # هامش صغير

        self._update_image_display()
        self._update_zoom_display()

    def _update_zoom_display(self):
        """تحديث عرض مستوى التكبير"""
        percentage = int(self.zoom_factor * 100)
        self.zoom_label.config(text=f"{percentage}%")

    def _on_canvas_click(self, event):
        """معالجة النقر على الكانفاس"""
        self.canvas.scan_mark(event.x, event.y)

    def _on_canvas_drag(self, event):
        """معالجة سحب الكانفاس"""
        self.canvas.scan_dragto(event.x, event.y, gain=1)

    def _on_mouse_wheel(self, event):
        """معالجة عجلة الماوس للتكبير"""
        if not self.current_image:
            return

        # تحديد اتجاه التمرير
        if event.delta > 0 or event.num == 4:
            self._zoom_in()
        elif event.delta < 0 or event.num == 5:
            self._zoom_out()

    def load_image(self, image_path):
        """
        تحميل وعرض صورة محسن

        Args:
            image_path: مسار الصورة
        """
        try:
            if not PIL_AVAILABLE:
                self.canvas.delete("all")
                self.canvas.create_text(
                    self.canvas.winfo_width() // 2,
                    self.canvas.winfo_height() // 2,
                    text="PIL غير متوفر لعرض الصور",
                    font=self.theme.fonts['body'],
                    fill=self.theme.colors['danger']
                )
                return

            self.current_image_path = image_path

            # تحميل الصورة الأصلية
            self.current_image = Image.open(image_path)

            # إعادة تعيين التكبير
            self.zoom_factor = 1.0

            # عرض الصورة
            self._update_image_display()

            # تحديث معلومات الصورة
            self._update_info_display()

            # تحديث عرض التكبير
            self._update_zoom_display()

        except Exception as e:
            self.canvas.delete("all")
            self.canvas.create_text(
                self.canvas.winfo_width() // 2,
                self.canvas.winfo_height() // 2,
                text=f"خطأ في تحميل الصورة:\n{str(e)}",
                font=self.theme.fonts['body'],
                fill=self.theme.colors['danger'],
                justify=tk.CENTER
            )
            self._update_info_display()

    def _update_image_display(self):
        """تحديث عرض الصورة مع التكبير"""
        if not self.current_image:
            return

        # حساب الحجم الجديد
        original_width, original_height = self.current_image.size
        new_width = int(original_width * self.zoom_factor)
        new_height = int(original_height * self.zoom_factor)

        # تغيير حجم الصورة
        if self.zoom_factor != 1.0:
            resized_image = self.current_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        else:
            resized_image = self.current_image

        # تحويل للعرض في tkinter
        self.photo = ImageTk.PhotoImage(resized_image)

        # مسح الكانفاس وعرض الصورة الجديدة
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)

        # تحديث منطقة التمرير
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _update_info_display(self):
        """تحديث عرض معلومات الصورة"""
        if not self.current_image_path or not self.current_image:
            self.filename_var.set("لا توجد صورة محددة")
            self.size_var.set("")
            self.format_var.set("")
            self.filesize_var.set("")
            return

        # اسم الملف
        filename = Path(self.current_image_path).name
        self.filename_var.set(filename)

        # الأبعاد
        width, height = self.current_image.size
        self.size_var.set(f"{width} × {height}")

        # التنسيق
        format_info = self.current_image.format or "غير معروف"
        if hasattr(self.current_image, 'mode'):
            format_info += f" ({self.current_image.mode})"
        self.format_var.set(format_info)

        # حجم الملف
        try:
            file_size = Path(self.current_image_path).stat().st_size
            if file_size > 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} bytes"
            self.filesize_var.set(size_str)
        except:
            self.filesize_var.set("غير معروف")


class ResultsViewer:
    """
    عارض النتائج والتقارير المحسن
    Enhanced results and reports viewer
    """

    def __init__(self, parent, theme=None):
        """
        تهيئة عارض النتائج المحسن

        Args:
            parent: الإطار الأب
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.current_results = None

        # إنشاء لوحة التصور
        self.visualization_panel = None

        self._setup_ui()

    def _setup_ui(self):
        """إعداد واجهة عارض النتائج المحسنة"""
        # إطار رئيسي مع نمط محسن
        self.main_frame = ttk.LabelFrame(self.parent,
                                        text="📊 النتائج والتقارير المتقدمة",
                                        style='Card.TLabelFrame',
                                        padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط أدوات محسن
        self._create_enhanced_toolbar()

        # منطقة عرض النتائج مع تبويبات محسنة
        self.notebook = ttk.Notebook(self.main_frame, style='TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # تبويب الملخص التنفيذي
        self._create_executive_summary_tab()

        # تبويب التفاصيل المتقدمة
        self._create_detailed_analysis_tab()

        # تبويب التصور والرسوم البيانية
        self._create_visualization_tab()

        # تبويب التقارير والتصدير
        self._create_reports_tab()

        # تبويب المقارنات
        self._create_comparison_tab()

    def _create_enhanced_toolbar(self):
        """إنشاء شريط أدوات محسن"""
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # مجموعة أزرار الملفات
        file_group = ttk.LabelFrame(toolbar_frame, text="الملفات", padding="5")
        file_group.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(file_group, text="📁 تحميل نتائج",
                  command=self._load_results,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(file_group, text="💾 حفظ النتائج",
                  command=self._save_results,
                  style='Primary.TButton').pack(side=tk.LEFT)

        # مجموعة أزرار التصدير
        export_group = ttk.LabelFrame(toolbar_frame, text="التصدير", padding="5")
        export_group.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(export_group, text="📋 تقرير PDF",
                  command=self._export_pdf_report,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(export_group, text="📊 تقرير Excel",
                  command=self._export_excel_report,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(export_group, text="🖼️ الرسوم البيانية",
                  command=self._export_charts,
                  style='Success.TButton').pack(side=tk.LEFT)

        # مجموعة أزرار التحكم
        control_group = ttk.LabelFrame(toolbar_frame, text="التحكم", padding="5")
        control_group.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_group, text="🔄 تحديث",
                  command=self._refresh_results,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_group, text="🗑️ مسح",
                  command=self._clear_results,
                  style='Danger.TButton').pack(side=tk.LEFT)

        # مؤشر الحالة
        status_frame = ttk.Frame(toolbar_frame)
        status_frame.pack(side=tk.RIGHT, fill=tk.Y)

        self.results_status_var = tk.StringVar(value="لا توجد نتائج")
        ttk.Label(status_frame, textvariable=self.results_status_var,
                 style='Muted.TLabel').pack(side=tk.RIGHT, pady=5)

    def _create_executive_summary_tab(self):
        """إنشاء تبويب الملخص التنفيذي"""
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="📋 الملخص التنفيذي")

        # إطار التمرير
        canvas = tk.Canvas(self.summary_frame, bg=self.theme.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(self.summary_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # بطاقات الإحصائيات الرئيسية
        self._create_summary_cards(scrollable_frame)

        # ملخص نصي
        summary_text_frame = ttk.LabelFrame(scrollable_frame,
                                           text="📝 الملخص النصي",
                                           style='Card.TLabelFrame',
                                           padding="10")
        summary_text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.summary_text = scrolledtext.ScrolledText(
            summary_text_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['body'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary'],
            relief='flat',
            bd=1
        )
        self.summary_text.pack(fill=tk.BOTH, expand=True)

        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_summary_cards(self, parent):
        """إنشاء بطاقات الإحصائيات الرئيسية"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, padx=10, pady=10)

        # بيانات البطاقات
        self.summary_cards = {}
        card_data = [
            ("total_images", "إجمالي الصور", "🖼️", self.theme.colors['primary']),
            ("processed_images", "الصور المعالجة", "✅", self.theme.colors['success']),
            ("detected_objects", "الأهداف المكتشفة", "🎯", self.theme.colors['info']),
            ("processing_time", "وقت المعالجة", "⏱️", self.theme.colors['warning']),
            ("success_rate", "معدل النجاح", "📈", self.theme.colors['success']),
            ("environments", "البيئات المكتشفة", "🌍", self.theme.colors['accent'])
        ]

        # إنشاء البطاقات في شبكة
        for i, (key, title, icon, color) in enumerate(card_data):
            row = i // 3
            col = i % 3

            card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=2)
            card_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # محتوى البطاقة
            content_frame = tk.Frame(card_frame, bg=color)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

            # الأيقونة والعنوان
            header_frame = tk.Frame(content_frame, bg=color)
            header_frame.pack(fill=tk.X)

            tk.Label(header_frame, text=icon, font=('Arial', 20),
                    bg=color, fg=self.theme.colors['text_light']).pack(side=tk.LEFT)

            tk.Label(header_frame, text=title, font=self.theme.fonts['small'],
                    bg=color, fg=self.theme.colors['text_light']).pack(side=tk.LEFT, padx=(10, 0))

            # القيمة
            self.summary_cards[key] = tk.StringVar(value="0")
            tk.Label(content_frame, textvariable=self.summary_cards[key],
                    font=self.theme.fonts['heading_large'], bg=color,
                    fg=self.theme.colors['text_light']).pack(anchor=tk.W, pady=(5, 0))

        # تكوين الأعمدة للتوسيط
        for i in range(3):
            cards_frame.grid_columnconfigure(i, weight=1)

    def _create_detailed_analysis_tab(self):
        """إنشاء تبويب التفاصيل المتقدمة"""
        self.details_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.details_frame, text="🔍 التحليل المفصل")

        # إطار مقسم للتفاصيل
        details_paned = ttk.PanedWindow(self.details_frame, orient=tk.HORIZONTAL)
        details_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شجرة التفاصيل
        tree_frame = ttk.LabelFrame(details_paned, text="🌳 هيكل البيانات", padding="5")
        details_paned.add(tree_frame, weight=1)

        self.details_tree = ttk.Treeview(tree_frame,
                                        columns=('value', 'type', 'description'),
                                        show='tree headings')
        self.details_tree.heading('#0', text='المعيار')
        self.details_tree.heading('value', text='القيمة')
        self.details_tree.heading('type', text='النوع')
        self.details_tree.heading('description', text='الوصف')

        # أشرطة التمرير للشجرة
        tree_scroll_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.details_tree.yview)
        tree_scroll_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.details_tree.xview)
        self.details_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)

        self.details_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # لوحة التفاصيل المحددة
        details_info_frame = ttk.LabelFrame(details_paned, text="📄 تفاصيل العنصر المحدد", padding="5")
        details_paned.add(details_info_frame, weight=1)

        self.selected_item_text = scrolledtext.ScrolledText(
            details_info_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['monospace'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary']
        )
        self.selected_item_text.pack(fill=tk.BOTH, expand=True)

        # ربط حدث التحديد
        self.details_tree.bind('<<TreeviewSelect>>', self._on_tree_select)

    def _create_visualization_tab(self):
        """إنشاء تبويب التصور والرسوم البيانية"""
        self.visualization_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.visualization_frame, text="📊 التصور والرسوم البيانية")

        # إنشاء لوحة التصور
        self.visualization_panel = DataVisualizationPanel(self.visualization_frame, self.theme)

    def _create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        self.reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.reports_frame, text="📋 التقارير")

        # قوالب التقارير
        templates_frame = ttk.LabelFrame(self.reports_frame,
                                        text="📝 قوالب التقارير",
                                        style='Card.TLabelFrame',
                                        padding="10")
        templates_frame.pack(fill=tk.X, padx=10, pady=10)

        # أزرار قوالب التقارير
        template_buttons = [
            ("📋 تقرير شامل", self._generate_comprehensive_report),
            ("📊 تقرير إحصائي", self._generate_statistical_report),
            ("🎯 تقرير الأهداف", self._generate_objects_report),
            ("🌍 تقرير البيئات", self._generate_environments_report),
            ("⚡ تقرير الأداء", self._generate_performance_report)
        ]

        for i, (text, command) in enumerate(template_buttons):
            row = i // 3
            col = i % 3

            btn = ttk.Button(templates_frame, text=text, command=command,
                           style='Primary.TButton')
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

        # تكوين الأعمدة
        for i in range(3):
            templates_frame.grid_columnconfigure(i, weight=1)

        # معاينة التقرير
        preview_frame = ttk.LabelFrame(self.reports_frame,
                                      text="👁️ معاينة التقرير",
                                      style='Card.TLabelFrame',
                                      padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.report_preview = scrolledtext.ScrolledText(
            preview_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['body'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary']
        )
        self.report_preview.pack(fill=tk.BOTH, expand=True)

    def _create_comparison_tab(self):
        """إنشاء تبويب المقارنات"""
        self.comparison_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.comparison_frame, text="⚖️ المقارنات")

        # إطار لتحميل ملفات المقارنة
        comparison_files_frame = ttk.LabelFrame(self.comparison_frame,
                                               text="📁 ملفات المقارنة",
                                               style='Card.TLabelFrame',
                                               padding="10")
        comparison_files_frame.pack(fill=tk.X, padx=10, pady=10)

        # قائمة الملفات للمقارنة
        self.comparison_files = []
        self.comparison_listbox = tk.Listbox(comparison_files_frame, height=6)
        self.comparison_listbox.pack(fill=tk.X, pady=(0, 10))

        # أزرار إدارة ملفات المقارنة
        comparison_buttons_frame = ttk.Frame(comparison_files_frame)
        comparison_buttons_frame.pack(fill=tk.X)

        ttk.Button(comparison_buttons_frame, text="➕ إضافة ملف",
                  command=self._add_comparison_file).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(comparison_buttons_frame, text="➖ إزالة ملف",
                  command=self._remove_comparison_file).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(comparison_buttons_frame, text="🔄 مقارنة",
                  command=self._perform_comparison).pack(side=tk.LEFT)

        # نتائج المقارنة
        comparison_results_frame = ttk.LabelFrame(self.comparison_frame,
                                                 text="📊 نتائج المقارنة",
                                                 style='Card.TLabelFrame',
                                                 padding="10")
        comparison_results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.comparison_results_text = scrolledtext.ScrolledText(
            comparison_results_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['body'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary']
        )
        self.comparison_results_text.pack(fill=tk.BOTH, expand=True)

    def _on_tree_select(self, event):
        """معالجة تحديد عنصر في الشجرة"""
        selection = self.details_tree.selection()
        if selection:
            item = selection[0]
            item_data = self.details_tree.item(item)

            # عرض تفاصيل العنصر المحدد
            details = f"العنصر: {item_data['text']}\n"
            details += f"القيمة: {item_data['values'][0] if item_data['values'] else 'غير محدد'}\n"
            details += f"النوع: {item_data['values'][1] if len(item_data['values']) > 1 else 'غير محدد'}\n"
            details += f"الوصف: {item_data['values'][2] if len(item_data['values']) > 2 else 'غير محدد'}\n"

            self.selected_item_text.delete(1.0, tk.END)
            self.selected_item_text.insert(1.0, details)

    def _save_results(self):
        """حفظ النتائج الحالية"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للحفظ")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ النتائج",
            defaultextension=".json",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_results, f, ensure_ascii=False, indent=2, default=str)
                messagebox.showinfo("نجح", f"تم حفظ النتائج: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {e}")

    def _export_pdf_report(self):
        """تصدير تقرير PDF"""
        messagebox.showinfo("تصدير PDF", "ميزة تصدير PDF ستكون متوفرة قريباً")

    def _export_excel_report(self):
        """تصدير تقرير Excel"""
        messagebox.showinfo("تصدير Excel", "ميزة تصدير Excel ستكون متوفرة قريباً")

    def _export_charts(self):
        """تصدير الرسوم البيانية"""
        if self.visualization_panel:
            self.visualization_panel._save_charts()

    def _refresh_results(self):
        """تحديث النتائج"""
        if self.current_results:
            self.display_results(self.current_results)

    def _generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج لإنشاء التقرير")
            return

        report = self._create_comprehensive_report_content()
        self.report_preview.delete(1.0, tk.END)
        self.report_preview.insert(1.0, report)

    def _generate_statistical_report(self):
        """إنشاء تقرير إحصائي"""
        if not self.current_results:
            return

        report = self._create_statistical_report_content()
        self.report_preview.delete(1.0, tk.END)
        self.report_preview.insert(1.0, report)

    def _generate_objects_report(self):
        """إنشاء تقرير الأهداف"""
        if not self.current_results:
            return

        report = self._create_objects_report_content()
        self.report_preview.delete(1.0, tk.END)
        self.report_preview.insert(1.0, report)

    def _generate_environments_report(self):
        """إنشاء تقرير البيئات"""
        if not self.current_results:
            return

        report = self._create_environments_report_content()
        self.report_preview.delete(1.0, tk.END)
        self.report_preview.insert(1.0, report)

    def _generate_performance_report(self):
        """إنشاء تقرير الأداء"""
        if not self.current_results:
            return

        report = self._create_performance_report_content()
        self.report_preview.delete(1.0, tk.END)
        self.report_preview.insert(1.0, report)

    def _create_comprehensive_report_content(self):
        """إنشاء محتوى التقرير الشامل"""
        report = []
        report.append("=" * 60)
        report.append("تقرير شامل - تطبيق الإنقاذ والبحث المتقدم")
        report.append("=" * 60)
        report.append(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # الملخص التنفيذي
        report.append("📋 الملخص التنفيذي")
        report.append("-" * 30)
        total_images = self.current_results.get('total_images', 0)
        processed_images = self.current_results.get('processed_images', 0)
        processing_time = self.current_results.get('processing_time', 0)

        report.append(f"• إجمالي الصور: {total_images}")
        report.append(f"• الصور المعالجة: {processed_images}")
        report.append(f"• وقت المعالجة: {processing_time:.2f} ثانية")

        if total_images > 0:
            success_rate = (processed_images / total_images) * 100
            report.append(f"• معدل النجاح: {success_rate:.1f}%")

        # تحليل البيئات
        analysis_results = self.current_results.get('analysis_results', {})
        env_analysis = analysis_results.get('environment_analysis', {})

        if env_analysis:
            report.append("")
            report.append("🌍 تحليل البيئات")
            report.append("-" * 30)

            distribution = env_analysis.get('distribution_percentages', {})
            for env, percentage in distribution.items():
                report.append(f"• {env}: {percentage:.1f}%")

        # الأهداف المكتشفة
        detected_objects = analysis_results.get('total_objects', 0)
        if detected_objects > 0:
            report.append("")
            report.append("🎯 الأهداف المكتشفة")
            report.append("-" * 30)
            report.append(f"• إجمالي الأهداف: {detected_objects}")

        return "\n".join(report)

    def _create_statistical_report_content(self):
        """إنشاء محتوى التقرير الإحصائي"""
        report = []
        report.append("📊 التقرير الإحصائي")
        report.append("=" * 40)
        report.append("")

        # إحصائيات المعالجة
        total_images = self.current_results.get('total_images', 0)
        processed_images = self.current_results.get('processed_images', 0)
        processing_time = self.current_results.get('processing_time', 0)

        report.append("📈 إحصائيات المعالجة:")
        report.append(f"  - العدد الإجمالي: {total_images}")
        report.append(f"  - المعالج بنجاح: {processed_images}")
        report.append(f"  - الوقت الإجمالي: {processing_time:.2f}s")

        if processed_images > 0:
            avg_time = processing_time / processed_images
            report.append(f"  - متوسط الوقت/صورة: {avg_time:.3f}s")

        # إحصائيات البيئات
        analysis_results = self.current_results.get('analysis_results', {})
        env_analysis = analysis_results.get('environment_analysis', {})
        distribution = env_analysis.get('distribution_percentages', {})

        if distribution:
            report.append("")
            report.append("🌍 إحصائيات البيئات:")
            for env, pct in sorted(distribution.items(), key=lambda x: x[1], reverse=True):
                report.append(f"  - {env}: {pct:.1f}%")

        return "\n".join(report)

    def _create_objects_report_content(self):
        """إنشاء محتوى تقرير الأهداف"""
        report = []
        report.append("🎯 تقرير الأهداف المكتشفة")
        report.append("=" * 40)
        report.append("")

        analysis_results = self.current_results.get('analysis_results', {})
        total_objects = analysis_results.get('total_objects', 0)

        report.append(f"إجمالي الأهداف المكتشفة: {total_objects}")
        report.append("")

        # تفاصيل الأهداف (بيانات وهمية للمثال)
        object_types = {
            'أشخاص': 45,
            'مركبات': 23,
            'مباني': 67,
            'حطام': 12,
            'أخرى': 8
        }

        report.append("تفصيل الأهداف حسب النوع:")
        for obj_type, count in object_types.items():
            percentage = (count / sum(object_types.values())) * 100
            report.append(f"  - {obj_type}: {count} ({percentage:.1f}%)")

        return "\n".join(report)

    def _create_environments_report_content(self):
        """إنشاء محتوى تقرير البيئات"""
        report = []
        report.append("🌍 تقرير البيئات المكتشفة")
        report.append("=" * 40)
        report.append("")

        analysis_results = self.current_results.get('analysis_results', {})
        env_analysis = analysis_results.get('environment_analysis', {})
        distribution = env_analysis.get('distribution_percentages', {})

        if distribution:
            report.append("توزيع البيئات:")
            for env, pct in sorted(distribution.items(), key=lambda x: x[1], reverse=True):
                report.append(f"  - {env}: {pct:.1f}%")

            report.append("")
            report.append("تحليل البيئات:")

            # تحليل البيئة السائدة
            dominant_env = max(distribution.items(), key=lambda x: x[1])
            report.append(f"البيئة السائدة: {dominant_env[0]} ({dominant_env[1]:.1f}%)")

            # عدد البيئات المكتشفة
            detected_envs = len([env for env, pct in distribution.items() if pct > 0])
            report.append(f"عدد البيئات المكتشفة: {detected_envs}")

        return "\n".join(report)

    def _create_performance_report_content(self):
        """إنشاء محتوى تقرير الأداء"""
        report = []
        report.append("⚡ تقرير الأداء")
        report.append("=" * 40)
        report.append("")

        processing_time = self.current_results.get('processing_time', 0)
        processed_images = self.current_results.get('processed_images', 0)

        report.append("📊 مؤشرات الأداء:")
        report.append(f"  - الوقت الإجمالي: {processing_time:.2f} ثانية")

        if processed_images > 0:
            avg_time = processing_time / processed_images
            throughput = processed_images / processing_time if processing_time > 0 else 0

            report.append(f"  - متوسط الوقت/صورة: {avg_time:.3f} ثانية")
            report.append(f"  - معدل الإنتاجية: {throughput:.2f} صورة/ثانية")

        # تقييم الأداء
        report.append("")
        report.append("📈 تقييم الأداء:")

        if avg_time < 1.0:
            performance_rating = "ممتاز"
        elif avg_time < 2.0:
            performance_rating = "جيد جداً"
        elif avg_time < 5.0:
            performance_rating = "جيد"
        else:
            performance_rating = "يحتاج تحسين"

        report.append(f"  - تقييم السرعة: {performance_rating}")

        return "\n".join(report)

    def _add_comparison_file(self):
        """إضافة ملف للمقارنة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النتائج للمقارنة",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.comparison_files.append(file_path)
            self.comparison_listbox.insert(tk.END, Path(file_path).name)

    def _remove_comparison_file(self):
        """إزالة ملف من المقارنة"""
        selection = self.comparison_listbox.curselection()
        if selection:
            index = selection[0]
            self.comparison_listbox.delete(index)
            del self.comparison_files[index]

    def _perform_comparison(self):
        """تنفيذ المقارنة"""
        if len(self.comparison_files) < 2:
            messagebox.showwarning("تحذير", "يجب اختيار ملفين على الأقل للمقارنة")
            return

        # تحميل ملفات المقارنة وإجراء المقارنة
        comparison_results = "نتائج المقارنة:\n"
        comparison_results += "=" * 30 + "\n\n"

        for i, file_path in enumerate(self.comparison_files):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                comparison_results += f"ملف {i+1}: {Path(file_path).name}\n"
                comparison_results += f"  - الصور المعالجة: {data.get('processed_images', 0)}\n"
                comparison_results += f"  - وقت المعالجة: {data.get('processing_time', 0):.2f}s\n\n"

            except Exception as e:
                comparison_results += f"خطأ في تحميل {file_path}: {e}\n\n"

        self.comparison_results_text.delete(1.0, tk.END)
        self.comparison_results_text.insert(1.0, comparison_results)

    def _load_results(self):
        """تحميل ملف نتائج"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النتائج",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.load_results_file(file_path)

    def load_results_file(self, file_path):
        """
        تحميل ملف النتائج

        Args:
            file_path: مسار ملف النتائج
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.current_results = json.load(f)

            self._display_results()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل النتائج: {e}")

    def _display_results(self):
        """عرض النتائج المحملة"""
        if not self.current_results:
            return

        # عرض الملخص
        self._display_summary()

        # عرض التفاصيل
        self._display_details()

        # عرض الرسوم البيانية
        if MATPLOTLIB_AVAILABLE:
            self._display_charts()

    def _display_summary(self):
        """عرض ملخص النتائج"""
        self.summary_text.delete(1.0, tk.END)

        summary_lines = []

        # معلومات أساسية
        if 'execution_summary' in self.current_results:
            exec_summary = self.current_results['execution_summary']
            summary_lines.extend([
                "=== ملخص التنفيذ ===",
                f"وقت التنفيذ: {exec_summary.get('execution_time_formatted', 'غير محدد')}",
                f"إجمالي الصور: {exec_summary.get('total_images_found', 0)}",
                f"التصنيفات الناجحة: {exec_summary.get('successful_classifications', 0)}",
                f"معدل النجاح: {exec_summary.get('success_rate', 0):.1f}%",
                f"سرعة المعالجة: {exec_summary.get('processing_speed_images_per_second', 0):.2f} صورة/ثانية",
                ""
            ])

        # معلومات مجموعة البيانات
        if 'dataset_info' in self.current_results:
            dataset_info = self.current_results['dataset_info']
            summary_lines.extend([
                "=== معلومات مجموعة البيانات ===",
                f"دليل الإخراج: {dataset_info.get('output_directory', 'غير محدد')}",
                f"إجمالي الصور: {dataset_info.get('total_images', 0)}",
                ""
            ])

            if dataset_info.get('yolo_dataset'):
                summary_lines.extend([
                    "=== مجموعة بيانات YOLO ===",
                    f"ملف data.yaml: {dataset_info['yolo_dataset'].get('data_yaml', 'غير محدد')}",
                    ""
                ])

        self.summary_text.insert(1.0, "\n".join(summary_lines))

    def _display_details(self):
        """عرض تفاصيل النتائج"""
        # مسح البيانات السابقة
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        # إضافة البيانات الجديدة
        self._add_tree_data("النتائج", self.current_results, "")

    def _add_tree_data(self, name, data, parent):
        """إضافة بيانات للشجرة بشكل تكراري"""
        if isinstance(data, dict):
            node = self.details_tree.insert(parent, 'end', text=name, values=('',))
            for key, value in data.items():
                self._add_tree_data(key, value, node)
        elif isinstance(data, list):
            node = self.details_tree.insert(parent, 'end', text=f"{name} (قائمة)", values=(f"{len(data)} عنصر",))
            for i, item in enumerate(data[:10]):  # عرض أول 10 عناصر فقط
                self._add_tree_data(f"[{i}]", item, node)
            if len(data) > 10:
                self.details_tree.insert(node, 'end', text="...", values=(f"و {len(data) - 10} عنصر آخر",))
        else:
            self.details_tree.insert(parent, 'end', text=name, values=(str(data),))

    def _display_charts(self):
        """عرض الرسوم البيانية"""
        # مسح الرسوم السابقة
        for widget in self.chart_canvas_frame.winfo_children():
            widget.destroy()

        try:
            # إنشاء رسم بياني بسيط
            fig = Figure(figsize=(8, 6), dpi=100)

            # رسم توزيع البيئات إذا كان متوفراً
            if ('analysis_results' in self.current_results and
                'environment_analysis' in self.current_results['analysis_results']):

                env_data = self.current_results['analysis_results']['environment_analysis']
                if 'distribution_percentages' in env_data:
                    ax = fig.add_subplot(111)

                    environments = list(env_data['distribution_percentages'].keys())
                    percentages = list(env_data['distribution_percentages'].values())

                    ax.pie(percentages, labels=environments, autopct='%1.1f%%')
                    ax.set_title('توزيع البيئات')

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.chart_canvas_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            error_label = ttk.Label(self.chart_canvas_frame,
                                   text=f"خطأ في عرض الرسوم البيانية: {e}")
            error_label.pack(expand=True)

    def _export_report(self):
        """تصدير تقرير النتائج"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".txt",
            filetypes=[
                ("ملفات نصية", "*.txt"),
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.current_results, f, ensure_ascii=False, indent=2, default=str)
                else:
                    # تصدير كملف نصي
                    summary_content = self.summary_text.get(1.0, tk.END)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(summary_content)

                messagebox.showinfo("نجح", f"تم حفظ التقرير: {file_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {e}")

    def _clear_results(self):
        """مسح النتائج المعروضة"""
        self.current_results = None
        self.summary_text.delete(1.0, tk.END)

        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        if MATPLOTLIB_AVAILABLE:
            for widget in self.chart_canvas_frame.winfo_children():
                widget.destroy()

    def display_results(self, results):
        """
        عرض النتائج مباشرة مع التصور المحسن

        Args:
            results: النتائج المراد عرضها
        """
        self.current_results = results

        # تحديث حالة النتائج
        self.results_status_var.set("تم تحميل النتائج بنجاح")

        # تحديث بطاقات الملخص
        self._update_summary_cards()

        # تحديث النص التفصيلي
        self._update_summary_text()

        # تحديث شجرة التفاصيل
        self._update_details_tree()

        # تحديث التصور
        if self.visualization_panel:
            self.visualization_panel.update_data(results)

        # إنشاء تقرير افتراضي
        self._generate_comprehensive_report()

    def _update_summary_cards(self):
        """تحديث بطاقات الملخص"""
        if not self.current_results:
            return

        # استخراج البيانات
        total_images = self.current_results.get('total_images', 0)
        processed_images = self.current_results.get('processed_images', 0)
        processing_time = self.current_results.get('processing_time', 0)

        analysis_results = self.current_results.get('analysis_results', {})
        detected_objects = analysis_results.get('total_objects', 0)

        env_analysis = analysis_results.get('environment_analysis', {})
        environments = len([env for env, pct in env_analysis.get('distribution_percentages', {}).items() if pct > 0])

        # تحديث البطاقات
        self.summary_cards['total_images'].set(str(total_images))
        self.summary_cards['processed_images'].set(str(processed_images))
        self.summary_cards['detected_objects'].set(str(detected_objects))
        self.summary_cards['environments'].set(str(environments))

        # حساب معدل النجاح
        if total_images > 0:
            success_rate = (processed_images / total_images) * 100
            self.summary_cards['success_rate'].set(f"{success_rate:.1f}%")
        else:
            self.summary_cards['success_rate'].set("0%")

        # تنسيق وقت المعالجة
        if processing_time < 60:
            time_str = f"{processing_time:.1f}s"
        elif processing_time < 3600:
            minutes = processing_time // 60
            seconds = processing_time % 60
            time_str = f"{int(minutes)}m {seconds:.0f}s"
        else:
            hours = processing_time // 3600
            minutes = (processing_time % 3600) // 60
            time_str = f"{int(hours)}h {int(minutes)}m"

        self.summary_cards['processing_time'].set(time_str)

    def _update_summary_text(self):
        """تحديث النص التفصيلي للملخص"""
        if not self.current_results:
            return

        summary_content = self._create_comprehensive_report_content()
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary_content)

    def _update_details_tree(self):
        """تحديث شجرة التفاصيل"""
        # مسح الشجرة الحالية
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        if not self.current_results:
            return

        # إضافة العقد الرئيسية
        self._add_tree_node("", "📊 معلومات عامة", self._get_general_info())
        self._add_tree_node("", "⏱️ معلومات الأداء", self._get_performance_info())

        # إضافة تحليل البيئات إذا كان متوفراً
        analysis_results = self.current_results.get('analysis_results', {})
        if analysis_results:
            analysis_node = self.details_tree.insert("", "end", text="🔍 نتائج التحليل")

            env_analysis = analysis_results.get('environment_analysis', {})
            if env_analysis:
                env_node = self.details_tree.insert(analysis_node, "end", text="🌍 تحليل البيئات")

                distribution = env_analysis.get('distribution_percentages', {})
                for env, percentage in distribution.items():
                    self.details_tree.insert(env_node, "end", text=env,
                                            values=(f"{percentage:.1f}%", "نسبة مئوية", f"نسبة البيئة {env}"))

    def _add_tree_node(self, parent, title, data_dict):
        """إضافة عقدة للشجرة"""
        node = self.details_tree.insert(parent, "end", text=title)

        for key, value in data_dict.items():
            value_type = type(value).__name__
            description = f"معلومات {key}"
            self.details_tree.insert(node, "end", text=key,
                                    values=(str(value), value_type, description))

    def _get_general_info(self):
        """الحصول على المعلومات العامة"""
        return {
            "إجمالي الصور": self.current_results.get('total_images', 0),
            "الصور المعالجة": self.current_results.get('processed_images', 0),
            "تاريخ المعالجة": self.current_results.get('processing_date', 'غير محدد'),
            "إصدار التطبيق": self.current_results.get('app_version', '2.0.0')
        }

    def _get_performance_info(self):
        """الحصول على معلومات الأداء"""
        processing_time = self.current_results.get('processing_time', 0)
        processed_images = self.current_results.get('processed_images', 0)

        performance_info = {
            "وقت المعالجة الإجمالي": f"{processing_time:.2f} ثانية",
            "متوسط الوقت لكل صورة": f"{processing_time/processed_images:.3f} ثانية" if processed_images > 0 else "غير محدد",
            "معدل الإنتاجية": f"{processed_images/processing_time:.2f} صورة/ثانية" if processing_time > 0 else "غير محدد"
        }

        return performance_info


class AdvancedSettingsPanel:
    """
    لوحة الإعدادات المتقدمة
    Advanced settings panel with validation and presets
    """

    def __init__(self, parent, theme=None):
        """
        تهيئة لوحة الإعدادات المتقدمة

        Args:
            parent: الإطار الأب
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.settings = {}
        self.validators = {}
        self.presets = {}

        # تحميل الإعدادات الافتراضية
        self._load_default_settings()

        # إعداد الواجهة
        self._setup_ui()

    def _load_default_settings(self):
        """تحميل الإعدادات الافتراضية"""
        self.settings = {
            # إعدادات المعالجة
            'processing': {
                'batch_size': 16,
                'max_workers': 4,
                'memory_limit_mb': 2048,
                'use_gpu': False,
                'gpu_memory_fraction': 0.8,
                'enable_multiprocessing': True,
                'chunk_size': 1000
            },

            # إعدادات الصور
            'image': {
                'target_width': 512,
                'target_height': 512,
                'maintain_aspect_ratio': True,
                'interpolation_method': 'LANCZOS',
                'quality': 95,
                'format': 'JPEG',
                'enable_preprocessing': True,
                'normalize_brightness': False,
                'enhance_contrast': False
            },

            # إعدادات التحليل
            'analysis': {
                'enable_environment_detection': True,
                'enable_object_detection': True,
                'confidence_threshold': 0.5,
                'nms_threshold': 0.4,
                'max_detections': 100,
                'enable_tracking': False,
                'save_annotations': True
            },

            # إعدادات التصدير
            'export': {
                'enable_yolo_format': True,
                'enable_coco_format': False,
                'enable_pascal_voc': False,
                'include_metadata': True,
                'compress_output': False,
                'create_thumbnails': True,
                'thumbnail_size': 256
            },

            # إعدادات الواجهة
            'ui': {
                'language': 'arabic',
                'theme': 'light',
                'auto_save_interval': 300,
                'show_tooltips': True,
                'enable_animations': True,
                'log_level': 'INFO',
                'max_log_entries': 1000
            },

            # إعدادات الشبكة
            'network': {
                'enable_cloud_sync': False,
                'api_endpoint': '',
                'timeout_seconds': 30,
                'retry_attempts': 3,
                'enable_offline_mode': True
            }
        }

        # تحميل الإعدادات المحفوظة إذا وجدت
        self._load_saved_settings()

        # إعداد المدققات
        self._setup_validators()

        # إعداد القوالب المحددة مسبقاً
        self._setup_presets()

    def _setup_validators(self):
        """إعداد مدققات الإعدادات"""
        self.validators = {
            'processing.batch_size': lambda x: 1 <= x <= 128,
            'processing.max_workers': lambda x: 1 <= x <= 32,
            'processing.memory_limit_mb': lambda x: 512 <= x <= 16384,
            'processing.gpu_memory_fraction': lambda x: 0.1 <= x <= 1.0,
            'processing.chunk_size': lambda x: 100 <= x <= 10000,

            'image.target_width': lambda x: 64 <= x <= 4096,
            'image.target_height': lambda x: 64 <= x <= 4096,
            'image.quality': lambda x: 1 <= x <= 100,

            'analysis.confidence_threshold': lambda x: 0.0 <= x <= 1.0,
            'analysis.nms_threshold': lambda x: 0.0 <= x <= 1.0,
            'analysis.max_detections': lambda x: 1 <= x <= 1000,

            'export.thumbnail_size': lambda x: 64 <= x <= 512,

            'ui.auto_save_interval': lambda x: 60 <= x <= 3600,
            'ui.max_log_entries': lambda x: 100 <= x <= 10000,

            'network.timeout_seconds': lambda x: 5 <= x <= 300,
            'network.retry_attempts': lambda x: 1 <= x <= 10
        }

    def _setup_presets(self):
        """إعداد القوالب المحددة مسبقاً"""
        self.presets = {
            'fast_processing': {
                'name': 'معالجة سريعة',
                'description': 'إعدادات محسنة للسرعة',
                'settings': {
                    'processing.batch_size': 32,
                    'processing.max_workers': 8,
                    'image.target_width': 256,
                    'image.target_height': 256,
                    'image.quality': 80,
                    'analysis.confidence_threshold': 0.7
                }
            },

            'high_quality': {
                'name': 'جودة عالية',
                'description': 'إعدادات محسنة للجودة',
                'settings': {
                    'processing.batch_size': 8,
                    'image.target_width': 1024,
                    'image.target_height': 1024,
                    'image.quality': 98,
                    'analysis.confidence_threshold': 0.3,
                    'image.enable_preprocessing': True
                }
            },

            'balanced': {
                'name': 'متوازن',
                'description': 'توازن بين السرعة والجودة',
                'settings': {
                    'processing.batch_size': 16,
                    'processing.max_workers': 4,
                    'image.target_width': 512,
                    'image.target_height': 512,
                    'image.quality': 90,
                    'analysis.confidence_threshold': 0.5
                }
            },

            'gpu_optimized': {
                'name': 'محسن لكرت الرسوميات',
                'description': 'إعدادات محسنة لاستخدام GPU',
                'settings': {
                    'processing.use_gpu': True,
                    'processing.gpu_memory_fraction': 0.9,
                    'processing.batch_size': 64,
                    'analysis.enable_object_detection': True
                }
            },

            'minimal_resources': {
                'name': 'موارد محدودة',
                'description': 'إعدادات للأجهزة ذات الموارد المحدودة',
                'settings': {
                    'processing.batch_size': 4,
                    'processing.max_workers': 2,
                    'processing.memory_limit_mb': 1024,
                    'image.target_width': 256,
                    'image.target_height': 256,
                    'analysis.max_detections': 50
                }
            }
        }

    def _setup_ui(self):
        """إعداد واجهة لوحة الإعدادات"""
        # إطار رئيسي مع تمرير
        main_canvas = tk.Canvas(self.parent, bg=self.theme.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(self.parent, orient="vertical", command=main_canvas.yview)
        self.scrollable_frame = ttk.Frame(main_canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        # شريط أدوات الإعدادات
        self._create_settings_toolbar()

        # أقسام الإعدادات
        self._create_processing_settings()
        self._create_image_settings()
        self._create_analysis_settings()
        self._create_export_settings()
        self._create_ui_settings()
        self._create_network_settings()

        # تخطيط التمرير
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        main_canvas.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """معالجة عجلة الماوس للتمرير"""
        canvas = event.widget
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _create_settings_toolbar(self):
        """إنشاء شريط أدوات الإعدادات"""
        toolbar_frame = ttk.LabelFrame(self.scrollable_frame,
                                      text="🔧 إدارة الإعدادات",
                                      style='Card.TLabelFrame',
                                      padding="10")
        toolbar_frame.pack(fill=tk.X, padx=10, pady=10)

        # أزرار القوالب
        presets_frame = ttk.Frame(toolbar_frame)
        presets_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(presets_frame, text="القوالب المحددة مسبقاً:",
                 style='Body.TLabel').pack(anchor=tk.W)

        presets_buttons_frame = ttk.Frame(presets_frame)
        presets_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        for preset_id, preset_data in self.presets.items():
            btn = ttk.Button(presets_buttons_frame,
                           text=preset_data['name'],
                           command=lambda pid=preset_id: self._apply_preset(pid))
            btn.pack(side=tk.LEFT, padx=(0, 5))
            add_tooltip(btn, preset_data['description'])

        # أزرار التحكم
        control_frame = ttk.Frame(toolbar_frame)
        control_frame.pack(fill=tk.X)

        ttk.Button(control_frame, text="💾 حفظ الإعدادات",
                  command=self._save_settings,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_frame, text="📁 تحميل إعدادات",
                  command=self._load_settings,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_frame, text="🔄 إعادة تعيين",
                  command=self._reset_settings,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(control_frame, text="✅ تطبيق",
                  command=self._apply_settings,
                  style='Success.TButton').pack(side=tk.RIGHT)

        ttk.Button(control_frame, text="🔍 التحقق من الصحة",
                  command=self._validate_all_settings,
                  style='Info.TButton').pack(side=tk.RIGHT, padx=(0, 5))

    def _create_processing_settings(self):
        """إنشاء إعدادات المعالجة"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="⚙️ إعدادات المعالجة",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # حجم الدفعة
        self._create_integer_setting(frame, 'processing.batch_size',
                                    'حجم الدفعة:', 1, 128,
                                    'عدد الصور التي تتم معالجتها في كل دفعة')

        # عدد العمليات المتوازية
        self._create_integer_setting(frame, 'processing.max_workers',
                                    'عدد العمليات المتوازية:', 1, 32,
                                    'عدد العمليات التي تعمل بالتوازي')

        # حد الذاكرة
        self._create_integer_setting(frame, 'processing.memory_limit_mb',
                                    'حد الذاكرة (MB):', 512, 16384,
                                    'الحد الأقصى لاستخدام الذاكرة')

        # استخدام GPU
        self._create_boolean_setting(frame, 'processing.use_gpu',
                                    'استخدام كرت الرسوميات',
                                    'تمكين استخدام GPU للمعالجة')

        # نسبة ذاكرة GPU
        self._create_float_setting(frame, 'processing.gpu_memory_fraction',
                                  'نسبة ذاكرة GPU:', 0.1, 1.0,
                                  'نسبة ذاكرة GPU المستخدمة')

        # تمكين المعالجة المتعددة
        self._create_boolean_setting(frame, 'processing.enable_multiprocessing',
                                    'تمكين المعالجة المتعددة',
                                    'استخدام عدة معالجات للمعالجة')

    def _create_image_settings(self):
        """إنشاء إعدادات الصور"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="🖼️ إعدادات الصور",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # أبعاد الصورة المستهدفة
        dimensions_frame = ttk.Frame(frame)
        dimensions_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(dimensions_frame, text="الأبعاد المستهدفة:",
                 style='Body.TLabel').pack(anchor=tk.W)

        dims_input_frame = ttk.Frame(dimensions_frame)
        dims_input_frame.pack(fill=tk.X, pady=(5, 0))

        self._create_integer_setting(dims_input_frame, 'image.target_width',
                                    'العرض:', 64, 4096, 'عرض الصورة بالبكسل',
                                    pack_side=tk.LEFT)

        self._create_integer_setting(dims_input_frame, 'image.target_height',
                                    'الارتفاع:', 64, 4096, 'ارتفاع الصورة بالبكسل',
                                    pack_side=tk.LEFT, padx=(20, 0))

        # الحفاظ على نسبة العرض إلى الارتفاع
        self._create_boolean_setting(frame, 'image.maintain_aspect_ratio',
                                    'الحفاظ على نسبة العرض إلى الارتفاع',
                                    'الحفاظ على النسبة الأصلية للصورة')

        # طريقة التداخل
        self._create_choice_setting(frame, 'image.interpolation_method',
                                   'طريقة التداخل:',
                                   ['NEAREST', 'BILINEAR', 'BICUBIC', 'LANCZOS'],
                                   'طريقة تغيير حجم الصورة')

        # جودة الصورة
        self._create_integer_setting(frame, 'image.quality',
                                    'جودة الصورة:', 1, 100,
                                    'جودة ضغط الصورة (1-100)')

        # تنسيق الصورة
        self._create_choice_setting(frame, 'image.format',
                                   'تنسيق الصورة:',
                                   ['JPEG', 'PNG', 'BMP', 'TIFF'],
                                   'تنسيق حفظ الصورة')

        # تمكين المعالجة المسبقة
        self._create_boolean_setting(frame, 'image.enable_preprocessing',
                                    'تمكين المعالجة المسبقة',
                                    'تطبيق معالجة مسبقة على الصور')

    def _create_analysis_settings(self):
        """إنشاء إعدادات التحليل"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="🔍 إعدادات التحليل",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # تمكين كشف البيئات
        self._create_boolean_setting(frame, 'analysis.enable_environment_detection',
                                    'تمكين كشف البيئات',
                                    'تحليل نوع البيئة في الصور')

        # تمكين كشف الأهداف
        self._create_boolean_setting(frame, 'analysis.enable_object_detection',
                                    'تمكين كشف الأهداف',
                                    'كشف الأهداف في الصور')

        # عتبة الثقة
        self._create_float_setting(frame, 'analysis.confidence_threshold',
                                  'عتبة الثقة:', 0.0, 1.0,
                                  'الحد الأدنى للثقة في الكشف')

        # عتبة NMS
        self._create_float_setting(frame, 'analysis.nms_threshold',
                                  'عتبة NMS:', 0.0, 1.0,
                                  'عتبة Non-Maximum Suppression')

        # الحد الأقصى للكشوفات
        self._create_integer_setting(frame, 'analysis.max_detections',
                                    'الحد الأقصى للكشوفات:', 1, 1000,
                                    'أقصى عدد أهداف يتم كشفها')

        # تمكين التتبع
        self._create_boolean_setting(frame, 'analysis.enable_tracking',
                                    'تمكين تتبع الأهداف',
                                    'تتبع الأهداف عبر الإطارات')

        # حفظ التعليقات التوضيحية
        self._create_boolean_setting(frame, 'analysis.save_annotations',
                                    'حفظ التعليقات التوضيحية',
                                    'حفظ ملفات التعليقات التوضيحية')

    def _create_export_settings(self):
        """إنشاء إعدادات التصدير"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="📤 إعدادات التصدير",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # تنسيقات التصدير
        formats_frame = ttk.LabelFrame(frame, text="تنسيقات التصدير", padding="10")
        formats_frame.pack(fill=tk.X, pady=(0, 10))

        self._create_boolean_setting(formats_frame, 'export.enable_yolo_format',
                                    'تنسيق YOLO',
                                    'تصدير بتنسيق YOLO')

        self._create_boolean_setting(formats_frame, 'export.enable_coco_format',
                                    'تنسيق COCO',
                                    'تصدير بتنسيق COCO')

        self._create_boolean_setting(formats_frame, 'export.enable_pascal_voc',
                                    'تنسيق Pascal VOC',
                                    'تصدير بتنسيق Pascal VOC')

        # خيارات إضافية
        self._create_boolean_setting(frame, 'export.include_metadata',
                                    'تضمين البيانات الوصفية',
                                    'تضمين معلومات إضافية في التصدير')

        self._create_boolean_setting(frame, 'export.compress_output',
                                    'ضغط الإخراج',
                                    'ضغط ملفات الإخراج')

        self._create_boolean_setting(frame, 'export.create_thumbnails',
                                    'إنشاء صور مصغرة',
                                    'إنشاء نسخ مصغرة من الصور')

        # حجم الصور المصغرة
        self._create_integer_setting(frame, 'export.thumbnail_size',
                                    'حجم الصور المصغرة:', 64, 512,
                                    'حجم الصور المصغرة بالبكسل')

    def _create_ui_settings(self):
        """إنشاء إعدادات الواجهة"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="🎨 إعدادات الواجهة",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # اللغة
        self._create_choice_setting(frame, 'ui.language',
                                   'اللغة:',
                                   ['arabic', 'english'],
                                   'لغة الواجهة')

        # النمط
        self._create_choice_setting(frame, 'ui.theme',
                                   'النمط:',
                                   ['light', 'dark', 'auto'],
                                   'نمط الواجهة')

        # فترة الحفظ التلقائي
        self._create_integer_setting(frame, 'ui.auto_save_interval',
                                    'فترة الحفظ التلقائي (ثانية):', 60, 3600,
                                    'فترة الحفظ التلقائي للإعدادات')

        # إظهار التلميحات
        self._create_boolean_setting(frame, 'ui.show_tooltips',
                                    'إظهار التلميحات',
                                    'إظهار تلميحات المساعدة')

        # تمكين الرسوم المتحركة
        self._create_boolean_setting(frame, 'ui.enable_animations',
                                    'تمكين الرسوم المتحركة',
                                    'تمكين التأثيرات المتحركة')

        # مستوى السجل
        self._create_choice_setting(frame, 'ui.log_level',
                                   'مستوى السجل:',
                                   ['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                                   'مستوى تفصيل السجل')

    def _create_network_settings(self):
        """إنشاء إعدادات الشبكة"""
        frame = ttk.LabelFrame(self.scrollable_frame,
                              text="🌐 إعدادات الشبكة",
                              style='Card.TLabelFrame',
                              padding="15")
        frame.pack(fill=tk.X, padx=10, pady=10)

        # تمكين المزامنة السحابية
        self._create_boolean_setting(frame, 'network.enable_cloud_sync',
                                    'تمكين المزامنة السحابية',
                                    'مزامنة البيانات مع السحابة')

        # نقطة نهاية API
        self._create_string_setting(frame, 'network.api_endpoint',
                                   'نقطة نهاية API:',
                                   'عنوان خدمة API')

        # مهلة الاتصال
        self._create_integer_setting(frame, 'network.timeout_seconds',
                                    'مهلة الاتصال (ثانية):', 5, 300,
                                    'مهلة انتظار الاتصال')

        # عدد محاولات الإعادة
        self._create_integer_setting(frame, 'network.retry_attempts',
                                    'عدد محاولات الإعادة:', 1, 10,
                                    'عدد محاولات إعادة الاتصال')

        # تمكين الوضع غير المتصل
        self._create_boolean_setting(frame, 'network.enable_offline_mode',
                                    'تمكين الوضع غير المتصل',
                                    'العمل بدون اتصال بالإنترنت')

    def _create_integer_setting(self, parent, setting_key, label, min_val, max_val,
                               tooltip, pack_side=None, padx=(0, 0)):
        """إنشاء إعداد عدد صحيح"""
        setting_frame = ttk.Frame(parent)
        if pack_side:
            setting_frame.pack(side=pack_side, padx=padx)
        else:
            setting_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(setting_frame, text=label, style='Body.TLabel').pack(anchor=tk.W)

        control_frame = ttk.Frame(setting_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        # متغير للقيمة
        var = tk.IntVar(value=self._get_setting_value(setting_key))

        # شريط التمرير
        scale = ttk.Scale(control_frame, from_=min_val, to=max_val,
                         variable=var, orient=tk.HORIZONTAL, length=200)
        scale.pack(side=tk.LEFT, padx=(0, 10))

        # حقل الإدخال
        entry = ttk.Entry(control_frame, textvariable=var, width=8)
        entry.pack(side=tk.LEFT, padx=(0, 5))

        # تسمية القيمة
        value_label = ttk.Label(control_frame, text=str(var.get()), style='Muted.TLabel')
        value_label.pack(side=tk.LEFT)

        # تحديث التسمية عند تغيير القيمة
        def update_label(*args):
            value_label.config(text=str(var.get()))
            self._set_setting_value(setting_key, var.get())

        var.trace('w', update_label)

        # إضافة تلميح
        add_tooltip(scale, tooltip)
        add_tooltip(entry, tooltip)

        return var

    def _create_float_setting(self, parent, setting_key, label, min_val, max_val, tooltip):
        """إنشاء إعداد عدد عشري"""
        setting_frame = ttk.Frame(parent)
        setting_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(setting_frame, text=label, style='Body.TLabel').pack(anchor=tk.W)

        control_frame = ttk.Frame(setting_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        # متغير للقيمة
        var = tk.DoubleVar(value=self._get_setting_value(setting_key))

        # شريط التمرير
        scale = ttk.Scale(control_frame, from_=min_val, to=max_val,
                         variable=var, orient=tk.HORIZONTAL, length=200)
        scale.pack(side=tk.LEFT, padx=(0, 10))

        # حقل الإدخال
        entry = ttk.Entry(control_frame, textvariable=var, width=8)
        entry.pack(side=tk.LEFT, padx=(0, 5))

        # تسمية القيمة
        value_label = ttk.Label(control_frame, text=f"{var.get():.2f}", style='Muted.TLabel')
        value_label.pack(side=tk.LEFT)

        # تحديث التسمية عند تغيير القيمة
        def update_label(*args):
            value_label.config(text=f"{var.get():.2f}")
            self._set_setting_value(setting_key, var.get())

        var.trace('w', update_label)

        # إضافة تلميح
        add_tooltip(scale, tooltip)
        add_tooltip(entry, tooltip)

        return var

    def _create_boolean_setting(self, parent, setting_key, label, tooltip):
        """إنشاء إعداد منطقي"""
        setting_frame = ttk.Frame(parent)
        setting_frame.pack(fill=tk.X, pady=(0, 5))

        # متغير للقيمة
        var = tk.BooleanVar(value=self._get_setting_value(setting_key))

        # مربع الاختيار
        checkbox = ttk.Checkbutton(setting_frame, text=label, variable=var,
                                  command=lambda: self._set_setting_value(setting_key, var.get()))
        checkbox.pack(anchor=tk.W)

        # إضافة تلميح
        add_tooltip(checkbox, tooltip)

        return var

    def _create_choice_setting(self, parent, setting_key, label, choices, tooltip):
        """إنشاء إعداد اختيار من قائمة"""
        setting_frame = ttk.Frame(parent)
        setting_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(setting_frame, text=label, style='Body.TLabel').pack(anchor=tk.W)

        # متغير للقيمة
        var = tk.StringVar(value=self._get_setting_value(setting_key))

        # قائمة منسدلة
        combobox = ttk.Combobox(setting_frame, textvariable=var, values=choices,
                               state='readonly', width=20)
        combobox.pack(anchor=tk.W, pady=(5, 0))

        # تحديث القيمة عند التغيير
        combobox.bind('<<ComboboxSelected>>',
                     lambda e: self._set_setting_value(setting_key, var.get()))

        # إضافة تلميح
        add_tooltip(combobox, tooltip)

        return var

    def _create_string_setting(self, parent, setting_key, label, tooltip):
        """إنشاء إعداد نص"""
        setting_frame = ttk.Frame(parent)
        setting_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(setting_frame, text=label, style='Body.TLabel').pack(anchor=tk.W)

        # متغير للقيمة
        var = tk.StringVar(value=self._get_setting_value(setting_key))

        # حقل الإدخال
        entry = ttk.Entry(setting_frame, textvariable=var, width=40)
        entry.pack(anchor=tk.W, pady=(5, 0))

        # تحديث القيمة عند التغيير
        var.trace('w', lambda *args: self._set_setting_value(setting_key, var.get()))

        # إضافة تلميح
        add_tooltip(entry, tooltip)

        return var

    def _get_setting_value(self, setting_key):
        """الحصول على قيمة إعداد"""
        keys = setting_key.split('.')
        value = self.settings
        for key in keys:
            value = value.get(key, {})
        return value

    def _set_setting_value(self, setting_key, value):
        """تعيين قيمة إعداد"""
        keys = setting_key.split('.')
        current = self.settings
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value

    def _apply_preset(self, preset_id):
        """تطبيق قالب محدد مسبقاً"""
        if preset_id not in self.presets:
            return

        preset = self.presets[preset_id]

        # تأكيد التطبيق
        result = messagebox.askyesno(
            "تطبيق القالب",
            f"هل تريد تطبيق قالب '{preset['name']}'?\n\n{preset['description']}\n\nسيتم استبدال الإعدادات الحالية."
        )

        if result:
            # تطبيق إعدادات القالب
            for setting_key, value in preset['settings'].items():
                self._set_setting_value(setting_key, value)

            # إعادة تحميل الواجهة
            self._refresh_ui()

            messagebox.showinfo("نجح", f"تم تطبيق قالب '{preset['name']}' بنجاح")

    def _save_settings(self):
        """حفظ الإعدادات"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ الإعدادات",
            defaultextension=".json",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", f"تم حفظ الإعدادات: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {e}")

    def _load_settings(self):
        """تحميل الإعدادات"""
        file_path = filedialog.askopenfilename(
            title="تحميل الإعدادات",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)

                # دمج الإعدادات المحملة
                self._merge_settings(loaded_settings)

                # إعادة تحميل الواجهة
                self._refresh_ui()

                messagebox.showinfo("نجح", f"تم تحميل الإعدادات: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الإعدادات: {e}")

    def _reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        result = messagebox.askyesno(
            "إعادة تعيين الإعدادات",
            "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\n\nسيتم فقدان جميع التغييرات الحالية."
        )

        if result:
            self._load_default_settings()
            self._refresh_ui()
            messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات للقيم الافتراضية")

    def _apply_settings(self):
        """تطبيق الإعدادات"""
        # التحقق من صحة الإعدادات
        validation_result = self._validate_all_settings()

        if validation_result['valid']:
            # حفظ الإعدادات محلياً
            self._save_local_settings()
            messagebox.showinfo("نجح", "تم تطبيق الإعدادات بنجاح")
        else:
            messagebox.showerror("خطأ في التحقق",
                               f"توجد أخطاء في الإعدادات:\n\n" +
                               "\n".join(validation_result['errors']))

    def _validate_all_settings(self):
        """التحقق من صحة جميع الإعدادات"""
        errors = []

        for setting_key, validator in self.validators.items():
            value = self._get_setting_value(setting_key)
            try:
                if not validator(value):
                    errors.append(f"قيمة غير صحيحة لـ {setting_key}: {value}")
            except Exception as e:
                errors.append(f"خطأ في التحقق من {setting_key}: {e}")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    def _refresh_ui(self):
        """إعادة تحميل الواجهة"""
        # إعادة إنشاء الواجهة مع القيم الجديدة
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        self._create_settings_toolbar()
        self._create_processing_settings()
        self._create_image_settings()
        self._create_analysis_settings()
        self._create_export_settings()
        self._create_ui_settings()
        self._create_network_settings()

    def _merge_settings(self, new_settings):
        """دمج الإعدادات الجديدة مع الحالية"""
        def merge_dict(base, new):
            for key, value in new.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value

        merge_dict(self.settings, new_settings)

    def _save_local_settings(self):
        """حفظ الإعدادات محلياً"""
        try:
            settings_dir = Path.home() / '.rescue_app'
            settings_dir.mkdir(exist_ok=True)

            settings_file = settings_dir / 'settings.json'
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"فشل في حفظ الإعدادات محلياً: {e}")

    def _load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = Path.home() / '.rescue_app' / 'settings.json'
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                self._merge_settings(saved_settings)
        except Exception as e:
            print(f"فشل في تحميل الإعدادات المحفوظة: {e}")

    def get_settings(self):
        """الحصول على الإعدادات الحالية"""
        return self.settings.copy()

    def update_settings(self, new_settings):
        """تحديث الإعدادات"""
        self._merge_settings(new_settings)
        self._refresh_ui()


class HelpAndDocumentationSystem:
    """
    نظام المساعدة والوثائق المتقدم
    Advanced help and documentation system
    """

    def __init__(self, parent, theme=None):
        """
        تهيئة نظام المساعدة

        Args:
            parent: النافذة الأب
            theme: نظام الألوان والتصميم
        """
        self.parent = parent
        self.theme = theme or RescueGUITheme()
        self.help_window = None
        self.current_topic = None

        # محتوى المساعدة
        self.help_content = self._load_help_content()

        # دروس تعليمية
        self.tutorials = self._load_tutorials()

        # أسئلة شائعة
        self.faq = self._load_faq()

    def _load_help_content(self):
        """تحميل محتوى المساعدة"""
        return {
            'getting_started': {
                'title': '🚀 البدء السريع',
                'content': """
# البدء السريع مع تطبيق الإنقاذ والبحث

## الخطوات الأساسية:

### 1. تحديد مجلد الصور
- اذهب إلى تبويب "الملفات والمجلدات"
- اضغط على "تصفح" بجانب "مجلد الصور"
- اختر المجلد الذي يحتوي على الصور المراد تحليلها

### 2. تحديد مجلد الإخراج
- اضغط على "تصفح" بجانب "مجلد الإخراج"
- اختر المجلد لحفظ النتائج والتقارير

### 3. ضبط الإعدادات (اختياري)
- انتقل إلى تبويب "الإعدادات المتقدمة"
- اضبط الإعدادات حسب احتياجاتك
- أو استخدم أحد القوالب المحددة مسبقاً

### 4. بدء المعالجة
- اضغط على "بدء المعالجة" في تبويب "التحكم"
- راقب التقدم في نافذة المعالجة
- انتظر حتى اكتمال العملية

### 5. عرض النتائج
- انتقل إلى تبويب "النتائج والتحليلات"
- استعرض الإحصائيات والرسوم البيانية
- اطلع على التقارير المفصلة
                """
            },

            'image_processing': {
                'title': '🖼️ معالجة الصور',
                'content': """
# معالجة الصور المتقدمة

## أنواع الصور المدعومة:
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)
- GIF (.gif)

## إعدادات المعالجة:

### حجم الصورة المستهدف:
- يمكن تحديد أبعاد مخصصة
- أو استخدام الأحجام المحددة مسبقاً
- الحفاظ على نسبة العرض إلى الارتفاع

### جودة الضغط:
- قيم من 1 إلى 100
- 100 = أعلى جودة، حجم أكبر
- 80-90 = توازن جيد بين الجودة والحجم

### المعالجة المسبقة:
- تحسين السطوع والتباين
- إزالة الضوضاء
- تحسين الوضوح
                """
            },

            'environment_analysis': {
                'title': '🌍 تحليل البيئات',
                'content': """
# تحليل البيئات المختلفة

## أنواع البيئات المدعومة:

### البيئة البحرية 🌊
- كشف المياه والسواحل
- تحليل الأمواج والتيارات
- كشف القوارب والسفن

### البيئة الصحراوية 🏜️
- تحليل الكثبان الرملية
- كشف الواحات
- تحديد المعالم الصحراوية

### البيئة الساحلية 🏖️
- تحليل خط الساحل
- كشف الشواطئ والخلجان
- تحديد المناطق الآمنة للإنزال

### البيئة الحضرية 🏙️
- كشف المباني والطرق
- تحليل الكثافة السكانية
- تحديد المناطق المتضررة

### البيئة الجبلية ⛰️
- تحليل التضاريس
- كشف المنحدرات والوديان
- تحديد مسارات الوصول

## خوارزميات التحليل:
- التعلم العميق للتصنيف
- معالجة الصور الرقمية
- تحليل الألوان والأنسجة
                """
            },

            'object_detection': {
                'title': '🎯 كشف الأهداف',
                'content': """
# كشف الأهداف المتقدم

## أنواع الأهداف:

### الأشخاص 👥
- كشف الأفراد في الصور
- تحديد الوضعيات والحركات
- تقدير العدد والكثافة

### المركبات 🚗
- السيارات والشاحنات
- القوارب والسفن
- الطائرات والمروحيات

### المباني والهياكل 🏢
- المباني السكنية والتجارية
- الجسور والأنفاق
- المنشآت الصناعية

### الحطام والأضرار 💥
- المباني المتضررة
- الحطام والأنقاض
- المناطق المدمرة

## إعدادات الكشف:

### عتبة الثقة:
- قيم من 0.0 إلى 1.0
- 0.5 = توازن جيد
- قيم أعلى = دقة أكبر، كشوفات أقل

### عتبة NMS:
- منع الكشوفات المتداخلة
- قيم من 0.0 إلى 1.0
- 0.4 = إعداد افتراضي جيد

## نماذج الذكاء الاصطناعي:
- YOLO v5/v8 للكشف السريع
- Faster R-CNN للدقة العالية
- نماذج مخصصة للبيئات المختلفة
                """
            },

            'yolo_training': {
                'title': '🎓 تدريب نماذج YOLO',
                'content': """
# تدريب نماذج YOLO المخصصة

## متطلبات التدريب:

### البيانات:
- صور عالية الجودة
- تعليقات توضيحية دقيقة
- تنوع في البيئات والظروف

### الأجهزة:
- كرت رسوميات قوي (GPU)
- ذاكرة كافية (8GB+)
- مساحة تخزين كبيرة

## خطوات التدريب:

### 1. إعداد البيانات:
- تجميع الصور
- إنشاء التعليقات التوضيحية
- تقسيم البيانات (تدريب/اختبار)

### 2. تكوين النموذج:
- اختيار بنية النموذج
- ضبط المعاملات
- تحديد فئات الأهداف

### 3. عملية التدريب:
- تحميل البيانات
- تدريب النموذج
- مراقبة الأداء

### 4. التقييم والتحسين:
- اختبار النموذج
- تحليل النتائج
- تحسين الأداء

## نصائح للحصول على أفضل النتائج:
- استخدم صور متنوعة
- تأكد من دقة التعليقات
- راقب الإفراط في التدريب
- اختبر في بيئات مختلفة
                """
            },

            'troubleshooting': {
                'title': '🔧 حل المشاكل',
                'content': """
# حل المشاكل الشائعة

## مشاكل المعالجة:

### "لا توجد صور في المجلد"
- تأكد من وجود صور بالتنسيقات المدعومة
- تحقق من صلاحيات الوصول للمجلد
- تأكد من عدم وجود مجلدات فرعية فقط

### "نفدت الذاكرة"
- قلل حجم الدفعة في الإعدادات
- قلل حجم الصورة المستهدف
- أغلق التطبيقات الأخرى

### "المعالجة بطيئة جداً"
- فعل استخدام GPU إذا كان متوفراً
- زد عدد العمليات المتوازية
- استخدم قالب "معالجة سريعة"

## مشاكل النتائج:

### "دقة الكشف منخفضة"
- اضبط عتبة الثقة
- استخدم صور عالية الجودة
- فعل المعالجة المسبقة

### "كشوفات خاطئة كثيرة"
- ارفع عتبة الثقة
- استخدم نموذج مدرب للبيئة المحددة
- تحقق من جودة الصور

## مشاكل التصدير:

### "فشل في حفظ النتائج"
- تحقق من صلاحيات الكتابة
- تأكد من وجود مساحة كافية
- تحقق من صحة مسار الإخراج

### "ملفات التصدير تالفة"
- تأكد من اكتمال المعالجة
- تحقق من استقرار النظام
- أعد تشغيل التطبيق

## الحصول على المساعدة:
- راجع السجلات للتفاصيل
- استخدم وضع التشخيص
- اتصل بالدعم الفني
                """
            },

            'keyboard_shortcuts': {
                'title': '⌨️ اختصارات لوحة المفاتيح',
                'content': """
# اختصارات لوحة المفاتيح

## اختصارات عامة:
- **Ctrl + O**: تحديد مجلد الصور
- **Ctrl + S**: حفظ المشروع
- **Ctrl + N**: مشروع جديد
- **Ctrl + Q**: إغلاق التطبيق

## اختصارات المعالجة:
- **F5**: بدء المعالجة
- **Esc**: إيقاف المعالجة
- **F6**: إيقاف مؤقت/استئناف

## اختصارات التنقل:
- **Ctrl + 1**: الصفحة الرئيسية
- **Ctrl + 2**: الإعدادات
- **Ctrl + 3**: النتائج
- **Ctrl + 4**: المساعدة

## اختصارات معاينة الصور:
- **+**: تكبير الصورة
- **-**: تصغير الصورة
- **0**: ملائمة للنافذة
- **Ctrl + C**: نسخ الصورة
- **Ctrl + V**: لصق من الحافظة

## اختصارات النوافذ:
- **F11**: ملء الشاشة
- **Alt + F4**: إغلاق النافذة
- **Ctrl + M**: تصغير النافذة

## اختصارات المساعدة:
- **F1**: فتح المساعدة
- **Ctrl + F1**: البحث في المساعدة
- **Shift + F1**: مساعدة سياقية
                """
            }
        }

    def _load_tutorials(self):
        """تحميل الدروس التعليمية"""
        return {
            'basic_usage': {
                'title': '📚 الاستخدام الأساسي',
                'steps': [
                    {
                        'title': 'تحديد مجلد الصور',
                        'description': 'اختر المجلد الذي يحتوي على الصور المراد تحليلها',
                        'image': None,
                        'tips': [
                            'تأكد من وجود صور بالتنسيقات المدعومة',
                            'يمكن أن يحتوي المجلد على مجلدات فرعية',
                            'تجنب المجلدات التي تحتوي على ملفات كبيرة جداً'
                        ]
                    },
                    {
                        'title': 'ضبط الإعدادات',
                        'description': 'اضبط إعدادات المعالجة حسب احتياجاتك',
                        'image': None,
                        'tips': [
                            'استخدم القوالب المحددة مسبقاً للبداية',
                            'اضبط حجم الصورة حسب قوة جهازك',
                            'فعل GPU إذا كان متوفراً'
                        ]
                    },
                    {
                        'title': 'بدء المعالجة',
                        'description': 'ابدأ عملية تحليل الصور',
                        'image': None,
                        'tips': [
                            'راقب التقدم في نافذة المعالجة',
                            'يمكن إيقاف المعالجة في أي وقت',
                            'تأكد من عدم إغلاق التطبيق أثناء المعالجة'
                        ]
                    },
                    {
                        'title': 'عرض النتائج',
                        'description': 'استعرض النتائج والتحليلات',
                        'image': None,
                        'tips': [
                            'استخدم التبويبات المختلفة لعرض النتائج',
                            'احفظ التقارير للمراجعة لاحقاً',
                            'صدر النتائج بصيغ مختلفة'
                        ]
                    }
                ]
            },

            'advanced_features': {
                'title': '🎓 الميزات المتقدمة',
                'steps': [
                    {
                        'title': 'تخصيص الإعدادات',
                        'description': 'استخدم الإعدادات المتقدمة للحصول على أفضل النتائج',
                        'image': None,
                        'tips': [
                            'اضبط عتبة الثقة حسب دقة النتائج المطلوبة',
                            'استخدم المعالجة المسبقة للصور منخفضة الجودة',
                            'فعل التتبع للفيديوهات'
                        ]
                    },
                    {
                        'title': 'تدريب النماذج',
                        'description': 'درب نماذج مخصصة لبيئتك',
                        'image': None,
                        'tips': [
                            'اجمع بيانات متنوعة وعالية الجودة',
                            'استخدم تعليقات توضيحية دقيقة',
                            'اختبر النموذج في بيئات مختلفة'
                        ]
                    },
                    {
                        'title': 'التحليل المتقدم',
                        'description': 'استخدم أدوات التحليل المتقدمة',
                        'image': None,
                        'tips': [
                            'استخدم الرسوم البيانية لفهم البيانات',
                            'قارن النتائج بين مشاريع مختلفة',
                            'صدر التحليلات بصيغ مختلفة'
                        ]
                    }
                ]
            }
        }

    def _load_faq(self):
        """تحميل الأسئلة الشائعة"""
        return [
            {
                'question': 'ما هي أنواع الصور المدعومة؟',
                'answer': 'يدعم التطبيق صيغ JPEG، PNG، BMP، TIFF، وGIF. يُنصح باستخدام صيغ JPEG أو PNG للحصول على أفضل النتائج.'
            },
            {
                'question': 'كم من الوقت تستغرق معالجة الصور؟',
                'answer': 'يعتمد الوقت على عدد الصور وحجمها وقوة الجهاز. عادة ما تستغرق معالجة 100 صورة بين 5-15 دقيقة.'
            },
            {
                'question': 'هل يمكن استخدام التطبيق بدون اتصال بالإنترنت؟',
                'answer': 'نعم، يعمل التطبيق بالكامل دون اتصال بالإنترنت. الاتصال مطلوب فقط للتحديثات والمزامنة السحابية.'
            },
            {
                'question': 'كيف يمكن تحسين دقة كشف الأهداف؟',
                'answer': 'استخدم صور عالية الجودة، اضبط عتبة الثقة، فعل المعالجة المسبقة، واستخدم نماذج مدربة للبيئة المحددة.'
            },
            {
                'question': 'ما هي متطلبات النظام؟',
                'answer': 'الحد الأدنى: 4GB RAM، معالج رباعي النواة، 2GB مساحة فارغة. المُوصى به: 8GB+ RAM، GPU مخصص، 10GB+ مساحة فارغة.'
            },
            {
                'question': 'كيف يمكن تصدير النتائج؟',
                'answer': 'يمكن تصدير النتائج بصيغ مختلفة: JSON للبيانات، PDF للتقارير، Excel للجداول، وصيغ YOLO/COCO للتدريب.'
            },
            {
                'question': 'هل يمكن معالجة الفيديوهات؟',
                'answer': 'حالياً يدعم التطبيق الصور الثابتة فقط. دعم الفيديوهات سيكون متوفراً في الإصدارات القادمة.'
            },
            {
                'question': 'كيف يمكن الحصول على الدعم الفني؟',
                'answer': 'يمكن الحصول على الدعم من خلال قسم المساعدة في التطبيق، أو عبر البريد الإلكتروني، أو منتدى المجتمع.'
            }
        ]

    def show_help_window(self, topic=None):
        """عرض نافذة المساعدة"""
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.lift()
            if topic:
                self._navigate_to_topic(topic)
            return

        self._create_help_window()
        if topic:
            self._navigate_to_topic(topic)

    def _create_help_window(self):
        """إنشاء نافذة المساعدة"""
        self.help_window = tk.Toplevel(self.parent)
        self.help_window.title("المساعدة والوثائق - تطبيق الإنقاذ والبحث")
        self.help_window.geometry("1200x800")
        self.help_window.configure(bg=self.theme.colors['bg_primary'])

        # جعل النافذة في المقدمة
        self.help_window.transient(self.parent)

        # إعداد الواجهة
        self._setup_help_ui()

        # توسيط النافذة
        self._center_help_window()

    def _setup_help_ui(self):
        """إعداد واجهة المساعدة"""
        # شريط العنوان
        self._create_help_header()

        # الإطار الرئيسي
        main_frame = tk.Frame(self.help_window, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار مقسم
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # الشريط الجانبي للتنقل
        self._create_navigation_sidebar(paned_window)

        # منطقة المحتوى الرئيسية
        self._create_content_area(paned_window)

    def _create_help_header(self):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(self.help_window, bg=self.theme.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # الأيقونة والعنوان
        content_frame = tk.Frame(header_frame, bg=self.theme.colors['primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        tk.Label(content_frame, text="❓", font=('Arial', 32),
                bg=self.theme.colors['primary'], fg=self.theme.colors['text_light']).pack(side=tk.LEFT)

        title_frame = tk.Frame(content_frame, bg=self.theme.colors['primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(15, 0))

        tk.Label(title_frame, text="المساعدة والوثائق",
                font=self.theme.fonts['title_large'], bg=self.theme.colors['primary'],
                fg=self.theme.colors['text_light']).pack(anchor=tk.W)

        tk.Label(title_frame, text="دليل شامل لاستخدام تطبيق الإنقاذ والبحث",
                font=self.theme.fonts['body'], bg=self.theme.colors['primary'],
                fg=self.theme.colors['text_light']).pack(anchor=tk.W)

        # شريط البحث
        search_frame = tk.Frame(content_frame, bg=self.theme.colors['primary'])
        search_frame.pack(side=tk.RIGHT, fill=tk.Y)

        tk.Label(search_frame, text="البحث:", font=self.theme.fonts['body'],
                bg=self.theme.colors['primary'], fg=self.theme.colors['text_light']).pack(anchor=tk.E)

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30,
                               font=self.theme.fonts['body'])
        search_entry.pack(anchor=tk.E, pady=(5, 0))
        search_entry.bind('<Return>', self._search_help)

    def _create_navigation_sidebar(self, parent):
        """إنشاء الشريط الجانبي للتنقل"""
        nav_frame = ttk.LabelFrame(parent, text="📚 المحتويات", padding="10")
        parent.add(nav_frame, weight=1)

        # دفتر تبويبات للأقسام المختلفة
        self.nav_notebook = ttk.Notebook(nav_frame)
        self.nav_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب المواضيع
        topics_frame = ttk.Frame(self.nav_notebook)
        self.nav_notebook.add(topics_frame, text="📖 المواضيع")
        self._create_topics_tree(topics_frame)

        # تبويب الدروس التعليمية
        tutorials_frame = ttk.Frame(self.nav_notebook)
        self.nav_notebook.add(tutorials_frame, text="🎓 الدروس")
        self._create_tutorials_list(tutorials_frame)

        # تبويب الأسئلة الشائعة
        faq_frame = ttk.Frame(self.nav_notebook)
        self.nav_notebook.add(faq_frame, text="❓ أسئلة شائعة")
        self._create_faq_list(faq_frame)

    def _create_topics_tree(self, parent):
        """إنشاء شجرة المواضيع"""
        self.topics_tree = ttk.Treeview(parent, show='tree')
        self.topics_tree.pack(fill=tk.BOTH, expand=True)

        # إضافة المواضيع
        for topic_id, topic_data in self.help_content.items():
            self.topics_tree.insert('', 'end', iid=topic_id, text=topic_data['title'])

        # ربط حدث التحديد
        self.topics_tree.bind('<<TreeviewSelect>>', self._on_topic_select)

    def _create_tutorials_list(self, parent):
        """إنشاء قائمة الدروس التعليمية"""
        self.tutorials_listbox = tk.Listbox(parent, font=self.theme.fonts['body'])
        self.tutorials_listbox.pack(fill=tk.BOTH, expand=True)

        # إضافة الدروس
        for tutorial_id, tutorial_data in self.tutorials.items():
            self.tutorials_listbox.insert(tk.END, tutorial_data['title'])

        # ربط حدث التحديد
        self.tutorials_listbox.bind('<<ListboxSelect>>', self._on_tutorial_select)

    def _create_faq_list(self, parent):
        """إنشاء قائمة الأسئلة الشائعة"""
        self.faq_listbox = tk.Listbox(parent, font=self.theme.fonts['body'])
        self.faq_listbox.pack(fill=tk.BOTH, expand=True)

        # إضافة الأسئلة
        for i, faq_item in enumerate(self.faq):
            self.faq_listbox.insert(tk.END, f"{i+1}. {faq_item['question']}")

        # ربط حدث التحديد
        self.faq_listbox.bind('<<ListboxSelect>>', self._on_faq_select)

    def _create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        content_frame = ttk.LabelFrame(parent, text="📄 المحتوى", padding="10")
        parent.add(content_frame, weight=3)

        # شريط أدوات المحتوى
        toolbar_frame = ttk.Frame(content_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar_frame, text="🏠 الصفحة الرئيسية",
                  command=self._show_home_content).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="🖨️ طباعة",
                  command=self._print_content).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="💾 حفظ",
                  command=self._save_content).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="📤 مشاركة",
                  command=self._share_content).pack(side=tk.LEFT)

        # منطقة عرض المحتوى
        self.content_text = scrolledtext.ScrolledText(
            content_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['body'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary'],
            relief='flat',
            bd=1
        )
        self.content_text.pack(fill=tk.BOTH, expand=True)

        # عرض المحتوى الافتراضي
        self._show_home_content()

    def _show_home_content(self):
        """عرض المحتوى الرئيسي"""
        home_content = """
# مرحباً بك في نظام المساعدة والوثائق

## 🚀 تطبيق الإنقاذ والبحث المتقدم

هذا التطبيق مصمم لمساعدة فرق الإنقاذ والبحث في تحليل الصور وكشف الأهداف في البيئات المختلفة.

## 📚 كيفية استخدام هذا الدليل:

### 📖 المواضيع
- استعرض المواضيع المختلفة في الشريط الجانبي
- اضغط على أي موضوع لعرض تفاصيله
- استخدم البحث للعثور على معلومات محددة

### 🎓 الدروس التعليمية
- دروس خطوة بخطوة للمبتدئين
- أمثلة عملية وتطبيقية
- نصائح وحيل للاستخدام المتقدم

### ❓ الأسئلة الشائعة
- إجابات للأسئلة الأكثر شيوعاً
- حلول للمشاكل المعروفة
- نصائح لتحسين الأداء

## 🔍 البحث السريع:
استخدم مربع البحث في الأعلى للعثور على معلومات محددة بسرعة.

## 📞 الحصول على المساعدة:
إذا لم تجد ما تبحث عنه، يمكنك:
- مراجعة السجلات للتفاصيل التقنية
- الاتصال بفريق الدعم الفني
- زيارة منتدى المجتمع

---

**نصيحة:** استخدم اختصار F1 في أي وقت لفتح المساعدة السياقية.
        """

        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(1.0, home_content)

    def _on_topic_select(self, event):
        """معالجة تحديد موضوع"""
        selection = self.topics_tree.selection()
        if selection:
            topic_id = selection[0]
            self._show_topic_content(topic_id)

    def _on_tutorial_select(self, event):
        """معالجة تحديد درس تعليمي"""
        selection = self.tutorials_listbox.curselection()
        if selection:
            tutorial_index = selection[0]
            tutorial_id = list(self.tutorials.keys())[tutorial_index]
            self._show_tutorial_content(tutorial_id)

    def _on_faq_select(self, event):
        """معالجة تحديد سؤال شائع"""
        selection = self.faq_listbox.curselection()
        if selection:
            faq_index = selection[0]
            self._show_faq_content(faq_index)

    def _show_topic_content(self, topic_id):
        """عرض محتوى موضوع"""
        if topic_id in self.help_content:
            topic_data = self.help_content[topic_id]
            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, topic_data['content'])
            self.current_topic = topic_id

    def _show_tutorial_content(self, tutorial_id):
        """عرض محتوى درس تعليمي"""
        if tutorial_id in self.tutorials:
            tutorial_data = self.tutorials[tutorial_id]
            content = f"# {tutorial_data['title']}\n\n"

            for i, step in enumerate(tutorial_data['steps'], 1):
                content += f"## الخطوة {i}: {step['title']}\n\n"
                content += f"{step['description']}\n\n"

                if step['tips']:
                    content += "### نصائح:\n"
                    for tip in step['tips']:
                        content += f"- {tip}\n"
                    content += "\n"

                content += "---\n\n"

            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, content)

    def _show_faq_content(self, faq_index):
        """عرض محتوى سؤال شائع"""
        if 0 <= faq_index < len(self.faq):
            faq_item = self.faq[faq_index]
            content = f"# {faq_item['question']}\n\n"
            content += f"{faq_item['answer']}\n\n"
            content += "---\n\n"
            content += "هل كانت هذه الإجابة مفيدة؟ إذا كان لديك أسئلة أخرى، يرجى مراجعة الأقسام الأخرى أو الاتصال بالدعم الفني."

            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, content)

    def _search_help(self, event=None):
        """البحث في المساعدة"""
        query = self.search_var.get().lower()
        if not query:
            return

        results = []

        # البحث في المواضيع
        for topic_id, topic_data in self.help_content.items():
            if query in topic_data['title'].lower() or query in topic_data['content'].lower():
                results.append(('topic', topic_id, topic_data['title']))

        # البحث في الدروس
        for tutorial_id, tutorial_data in self.tutorials.items():
            if query in tutorial_data['title'].lower():
                results.append(('tutorial', tutorial_id, tutorial_data['title']))

        # البحث في الأسئلة الشائعة
        for i, faq_item in enumerate(self.faq):
            if query in faq_item['question'].lower() or query in faq_item['answer'].lower():
                results.append(('faq', i, faq_item['question']))

        # عرض النتائج
        self._show_search_results(query, results)

    def _show_search_results(self, query, results):
        """عرض نتائج البحث"""
        content = f"# نتائج البحث عن: '{query}'\n\n"

        if not results:
            content += "لم يتم العثور على نتائج. جرب كلمات مفتاحية أخرى.\n\n"
            content += "نصائح للبحث:\n"
            content += "- استخدم كلمات مفتاحية بسيطة\n"
            content += "- جرب مرادفات مختلفة\n"
            content += "- تأكد من الإملاء الصحيح\n"
        else:
            content += f"تم العثور على {len(results)} نتيجة:\n\n"

            for result_type, result_id, result_title in results:
                if result_type == 'topic':
                    content += f"📖 **موضوع:** {result_title}\n"
                elif result_type == 'tutorial':
                    content += f"🎓 **درس:** {result_title}\n"
                elif result_type == 'faq':
                    content += f"❓ **سؤال شائع:** {result_title}\n"
                content += "\n"

        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(1.0, content)

    def _navigate_to_topic(self, topic):
        """التنقل إلى موضوع محدد"""
        if topic in self.help_content:
            self.topics_tree.selection_set(topic)
            self._show_topic_content(topic)

    def _center_help_window(self):
        """توسيط نافذة المساعدة"""
        self.help_window.update_idletasks()
        width = self.help_window.winfo_width()
        height = self.help_window.winfo_height()
        x = (self.help_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.help_window.winfo_screenheight() // 2) - (height // 2)
        self.help_window.geometry(f'{width}x{height}+{x}+{y}')

    def _print_content(self):
        """طباعة المحتوى"""
        messagebox.showinfo("طباعة", "ميزة الطباعة ستكون متوفرة قريباً")

    def _save_content(self):
        """حفظ المحتوى"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ المحتوى",
            defaultextension=".txt",
            filetypes=[
                ("ملفات نصية", "*.txt"),
                ("ملفات Markdown", "*.md"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                content = self.content_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("نجح", f"تم حفظ المحتوى: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المحتوى: {e}")

    def _share_content(self):
        """مشاركة المحتوى"""
        messagebox.showinfo("مشاركة", "ميزة المشاركة ستكون متوفرة قريباً")

    def show_contextual_help(self, context):
        """عرض مساعدة سياقية"""
        context_topics = {
            'image_preview': 'image_processing',
            'settings': 'getting_started',
            'results': 'environment_analysis',
            'yolo_training': 'yolo_training'
        }

        topic = context_topics.get(context, 'getting_started')
        self.show_help_window(topic)

    def close_help(self):
        """إغلاق نافذة المساعدة"""
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.destroy()
            self.help_window = None


class EnhancedRescueGUI:
    """
    الواجهة الرسومية المحسنة لتطبيق الإنقاذ والبحث
    Enhanced GUI for Search and Rescue Application
    """

    def __init__(self):
        """تهيئة الواجهة الرسومية المحسنة"""
        if not GUI_AVAILABLE:
            raise ImportError("tkinter غير متوفر للواجهة الرسومية")

        # النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("تطبيق الإنقاذ والبحث المتقدم - Advanced Search & Rescue Application")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)

        # نظام الألوان والتصميم
        self.theme = RescueGUITheme()
        self.theme.configure_ttk_styles()

        # متغيرات الواجهة
        self.input_directory = tk.StringVar()
        self.output_directory = tk.StringVar()
        self.config_file = tk.StringVar()

        # متغيرات الإعدادات
        self.target_size_width = tk.IntVar(value=512)
        self.target_size_height = tk.IntVar(value=512)
        self.batch_size = tk.IntVar(value=16)
        self.enable_analytics = tk.BooleanVar(value=True)
        self.enable_yolo_export = tk.BooleanVar(value=True)
        self.verbose_mode = tk.BooleanVar(value=False)

        # حالة التطبيق
        self.processing_thread = None
        self.progress_dialog = None
        self.current_operation = None

        # قائمة انتظار الرسائل
        self.message_queue = queue.Queue()

        # إعداد الواجهة
        self._setup_ui()
        self._setup_menu()

        # بدء معالج الرسائل
        self._start_message_handler()

        self.logger = logging.getLogger(__name__)

    def _setup_ui(self):
        """إعداد عناصر الواجهة الرئيسية المحسنة"""
        # تطبيق نمط النافذة الرئيسية
        self.root.configure(bg=self.theme.colors['bg_primary'])

        # إطار رئيسي مع حشو محسن
        main_frame = tk.Frame(self.root, bg=self.theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=self.theme.spacing['lg'], pady=self.theme.spacing['lg'])

        # شريط العنوان المحسن
        self._create_enhanced_header(main_frame)

        # شريط التنقل السريع
        self._create_navigation_bar(main_frame)

        # الإطار الرئيسي للمحتوى مع تخطيط محسن
        self.content_frame = tk.Frame(main_frame, bg=self.theme.colors['bg_primary'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(self.theme.spacing['lg'], 0))

        # إعداد التخطيط المرن
        self._setup_responsive_layout()

        # شريط الحالة المحسن
        self._create_enhanced_status_bar(main_frame)

        # إعداد اختصارات لوحة المفاتيح
        self._setup_keyboard_shortcuts()

    def _setup_responsive_layout(self):
        """إعداد التخطيط المرن والمتجاوب"""
        # إنشاء إطار قابل للتقسيم
        self.paned_window = ttk.PanedWindow(self.content_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)

        # اللوحة اليسرى - الإعدادات والتحكم
        self.left_panel = self._create_enhanced_left_panel()
        self.paned_window.add(self.left_panel, weight=1)

        # اللوحة اليمنى - المعاينة والنتائج
        self.right_panel = self._create_enhanced_right_panel()
        self.paned_window.add(self.right_panel, weight=2)

        # تعيين الحد الأدنى لعرض الألواح
        self.paned_window.paneconfigure(self.left_panel, minsize=300)
        self.paned_window.paneconfigure(self.right_panel, minsize=400)

    def _create_enhanced_header(self, parent):
        """إنشاء شريط العنوان المحسن"""
        header_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, self.theme.spacing['lg']))
        header_frame.pack_propagate(False)

        # إطار للمحتوى مع خلفية متدرجة (محاكاة)
        content_frame = tk.Frame(header_frame,
                                bg=self.theme.colors['primary'],
                                relief='flat',
                                bd=0)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # الجانب الأيسر - العنوان والأيقونة
        left_section = tk.Frame(content_frame, bg=self.theme.colors['primary'])
        left_section.pack(side=tk.LEFT, fill=tk.Y, padx=self.theme.spacing['lg'], pady=self.theme.spacing['md'])

        # أيقونة التطبيق
        icon_label = tk.Label(left_section,
                             text="🚁",
                             font=('Arial', 32),
                             bg=self.theme.colors['primary'],
                             fg=self.theme.colors['text_light'])
        icon_label.pack(side=tk.LEFT, padx=(0, self.theme.spacing['lg']))

        # معلومات التطبيق
        info_frame = tk.Frame(left_section, bg=self.theme.colors['primary'])
        info_frame.pack(side=tk.LEFT, fill=tk.Y)

        title_label = tk.Label(info_frame,
                              text="تطبيق الإنقاذ والبحث المتقدم",
                              font=self.theme.fonts['title_large'],
                              bg=self.theme.colors['primary'],
                              fg=self.theme.colors['text_light'])
        title_label.pack(anchor=tk.W)

        subtitle_label = tk.Label(info_frame,
                                 text="Advanced Search & Rescue Application",
                                 font=self.theme.fonts['body'],
                                 bg=self.theme.colors['primary'],
                                 fg=self.theme.colors['text_light'])
        subtitle_label.pack(anchor=tk.W)

        # الجانب الأيمن - معلومات الحالة والإصدار
        right_section = tk.Frame(content_frame, bg=self.theme.colors['primary'])
        right_section.pack(side=tk.RIGHT, fill=tk.Y, padx=self.theme.spacing['lg'], pady=self.theme.spacing['md'])

        version_label = tk.Label(right_section,
                                text="الإصدار 2.0.0",
                                font=self.theme.fonts['small'],
                                bg=self.theme.colors['primary'],
                                fg=self.theme.colors['text_light'])
        version_label.pack(anchor=tk.E)

        # مؤشر الحالة
        self.status_indicator = tk.Label(right_section,
                                        text="● جاهز",
                                        font=self.theme.fonts['body'],
                                        bg=self.theme.colors['primary'],
                                        fg=self.theme.colors['success_light'])
        self.status_indicator.pack(anchor=tk.E, pady=(self.theme.spacing['sm'], 0))

    def _create_navigation_bar(self, parent):
        """إنشاء شريط التنقل السريع"""
        nav_frame = tk.Frame(parent, bg=self.theme.colors['bg_secondary'], height=50)
        nav_frame.pack(fill=tk.X, pady=(0, self.theme.spacing['lg']))
        nav_frame.pack_propagate(False)

        # أزرار التنقل السريع
        nav_buttons = [
            ("🏠", "الرئيسية", self._show_home_view),
            ("📁", "تحديد الملفات", self._quick_file_selection),
            ("⚙️", "الإعدادات", self._show_settings_view),
            ("📊", "النتائج", self._show_results_view),
            ("❓", "المساعدة", self._show_help_view)
        ]

        for icon, text, command in nav_buttons:
            btn_frame = tk.Frame(nav_frame, bg=self.theme.colors['bg_secondary'])
            btn_frame.pack(side=tk.LEFT, padx=self.theme.spacing['sm'], pady=self.theme.spacing['sm'])

            btn = tk.Button(btn_frame,
                           text=f"{icon}\n{text}",
                           font=self.theme.fonts['small'],
                           bg=self.theme.colors['bg_card'],
                           fg=self.theme.colors['text_primary'],
                           relief='flat',
                           bd=0,
                           padx=self.theme.spacing['md'],
                           pady=self.theme.spacing['sm'],
                           command=command,
                           cursor='hand2')
            btn.pack()

            # تطبيق تأثير التمرير
            self.theme.apply_hover_effect(btn,
                                         self.theme.colors['bg_hover'],
                                         self.theme.colors['bg_card'])

    def _setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصارات عامة
        self.root.bind('<Control-o>', lambda e: self._quick_file_selection())
        self.root.bind('<Control-s>', lambda e: self._save_project())
        self.root.bind('<Control-n>', lambda e: self._new_project())
        self.root.bind('<F1>', lambda e: self._show_help_view())
        self.root.bind('<F5>', lambda e: self._start_processing())
        self.root.bind('<Escape>', lambda e: self._stop_processing())

        # اختصارات التنقل
        self.root.bind('<Control-1>', lambda e: self._show_home_view())
        self.root.bind('<Control-2>', lambda e: self._show_settings_view())
        self.root.bind('<Control-3>', lambda e: self._show_results_view())

    def _create_enhanced_left_panel(self):
        """إنشاء اللوحة اليسرى المحسنة"""
        left_frame = tk.Frame(self.paned_window, bg=self.theme.colors['bg_secondary'])

        # دفتر التبويبات المحسن للإعدادات
        self.settings_notebook = ttk.Notebook(left_frame, style='TNotebook')
        self.settings_notebook.pack(fill=tk.BOTH, expand=True, padx=self.theme.spacing['md'], pady=self.theme.spacing['md'])

        # تبويب الإعدادات الأساسية
        self.basic_tab = ttk.Frame(self.settings_notebook)
        self.settings_notebook.add(self.basic_tab, text="📁 الملفات والمجلدات")
        self._create_enhanced_basic_settings(self.basic_tab)

        # تبويب الإعدادات المتقدمة
        self.advanced_tab = ttk.Frame(self.settings_notebook)
        self.settings_notebook.add(self.advanced_tab, text="⚙️ الإعدادات المتقدمة")

        # إنشاء لوحة الإعدادات المتقدمة
        self.advanced_settings_panel = AdvancedSettingsPanel(self.advanced_tab, self.theme)

        # تبويب التحكم والعمليات
        self.control_tab = ttk.Frame(self.settings_notebook)
        self.settings_notebook.add(self.control_tab, text="🎮 التحكم")
        self._create_enhanced_control_panel(self.control_tab)

        return left_frame

    def _create_enhanced_right_panel(self):
        """إنشاء اللوحة اليمنى المحسنة"""
        right_frame = tk.Frame(self.paned_window, bg=self.theme.colors['bg_primary'])

        # دفتر التبويبات للمعاينة والنتائج
        self.preview_notebook = ttk.Notebook(right_frame, style='TNotebook')
        self.preview_notebook.pack(fill=tk.BOTH, expand=True, padx=self.theme.spacing['md'], pady=self.theme.spacing['md'])

        # تبويب معاينة الصور المحسن
        self.preview_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(self.preview_tab, text="🖼️ معاينة الصور")
        self.image_preview = ImagePreviewPanel(self.preview_tab, self.theme)

        # تبويب النتائج والتحليلات
        self.results_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(self.results_tab, text="📊 النتائج والتحليلات")
        self.results_viewer = ResultsViewer(self.results_tab)

        # تبويب السجلات والمراقبة
        self.logs_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(self.logs_tab, text="📋 السجلات")
        self._create_logs_panel(self.logs_tab)

        return right_frame

    def _create_logs_panel(self, parent):
        """إنشاء لوحة السجلات"""
        logs_frame = ttk.LabelFrame(parent, text="📋 سجل العمليات", style='Card.TLabelFrame', padding="10")
        logs_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط أدوات السجلات
        toolbar = ttk.Frame(logs_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar, text="🗑️ مسح السجل",
                  command=self._clear_logs,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar, text="💾 حفظ السجل",
                  command=self._save_logs,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))

        # منطقة عرض السجلات
        self.logs_text = scrolledtext.ScrolledText(
            logs_frame,
            wrap=tk.WORD,
            font=self.theme.fonts['monospace'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary'],
            relief='flat',
            bd=1
        )
        self.logs_text.pack(fill=tk.BOTH, expand=True)

        # إضافة رسالة ترحيب
        self._add_log_message("تم تشغيل التطبيق بنجاح", "INFO")

    def _add_log_message(self, message: str, level: str = "INFO"):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # تحديد لون الرسالة حسب المستوى
        color_map = {
            "INFO": self.theme.colors['info'],
            "WARNING": self.theme.colors['warning'],
            "ERROR": self.theme.colors['danger'],
            "SUCCESS": self.theme.colors['success']
        }

        color = color_map.get(level, self.theme.colors['text_primary'])

        # إضافة الرسالة
        self.logs_text.insert(tk.END, f"[{timestamp}] [{level}] {message}\n")
        self.logs_text.see(tk.END)

        # تطبيق اللون (إذا كان مدعوماً)
        try:
            start_line = self.logs_text.index(tk.END + "-2l linestart")
            end_line = self.logs_text.index(tk.END + "-1l lineend")
            self.logs_text.tag_add(level, start_line, end_line)
            self.logs_text.tag_config(level, foreground=color)
        except:
            pass

    def _clear_logs(self):
        """مسح السجل"""
        self.logs_text.delete(1.0, tk.END)
        self._add_log_message("تم مسح السجل", "INFO")

    def _save_logs(self):
        """حفظ السجل"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ السجل",
            defaultextension=".log",
            filetypes=[
                ("ملفات السجل", "*.log"),
                ("ملفات نصية", "*.txt"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                content = self.logs_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self._add_log_message(f"تم حفظ السجل: {file_path}", "SUCCESS")
            except Exception as e:
                self._add_log_message(f"فشل في حفظ السجل: {e}", "ERROR")

    def _create_enhanced_status_bar(self, parent):
        """إنشاء شريط الحالة المحسن"""
        status_frame = tk.Frame(parent, bg=self.theme.colors['bg_secondary'], height=40)
        status_frame.pack(fill=tk.X, pady=(self.theme.spacing['lg'], 0))
        status_frame.pack_propagate(False)

        # خط فاصل
        separator = ttk.Separator(status_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 5))

        # إطار المحتوى
        content_frame = tk.Frame(status_frame, bg=self.theme.colors['bg_secondary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=self.theme.spacing['lg'], pady=self.theme.spacing['sm'])

        # الجانب الأيسر - حالة التطبيق
        left_status = tk.Frame(content_frame, bg=self.theme.colors['bg_secondary'])
        left_status.pack(side=tk.LEFT, fill=tk.Y)

        self.status_var = tk.StringVar(value="جاهز")
        status_label = tk.Label(left_status,
                               textvariable=self.status_var,
                               font=self.theme.fonts['body'],
                               bg=self.theme.colors['bg_secondary'],
                               fg=self.theme.colors['text_primary'])
        status_label.pack(side=tk.LEFT)

        # شريط التقدم المصغر
        self.mini_progress = ttk.Progressbar(left_status,
                                           length=100,
                                           mode='determinate',
                                           style='TProgressbar')
        self.mini_progress.pack(side=tk.LEFT, padx=(10, 0))
        self.mini_progress.pack_forget()  # مخفي افتراضياً

        # الوسط - معلومات العملية الحالية
        center_status = tk.Frame(content_frame, bg=self.theme.colors['bg_secondary'])
        center_status.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 20))

        self.operation_var = tk.StringVar(value="")
        operation_label = tk.Label(center_status,
                                  textvariable=self.operation_var,
                                  font=self.theme.fonts['small'],
                                  bg=self.theme.colors['bg_secondary'],
                                  fg=self.theme.colors['text_muted'])
        operation_label.pack()

        # الجانب الأيمن - معلومات النظام
        right_status = tk.Frame(content_frame, bg=self.theme.colors['bg_secondary'])
        right_status.pack(side=tk.RIGHT, fill=tk.Y)

        # معلومات الذاكرة والأداء
        self.memory_var = tk.StringVar(value="")
        memory_label = tk.Label(right_status,
                               textvariable=self.memory_var,
                               font=self.theme.fonts['small'],
                               bg=self.theme.colors['bg_secondary'],
                               fg=self.theme.colors['text_muted'])
        memory_label.pack(side=tk.RIGHT, padx=(10, 0))

        # الوقت
        self.time_var = tk.StringVar()
        time_label = tk.Label(right_status,
                             textvariable=self.time_var,
                             font=self.theme.fonts['small'],
                             bg=self.theme.colors['bg_secondary'],
                             fg=self.theme.colors['text_muted'])
        time_label.pack(side=tk.RIGHT)

        # تحديث الوقت
        self._update_time()

    def _update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self._update_time)

    # طرق التنقل
    def _show_home_view(self):
        """عرض الصفحة الرئيسية"""
        self.settings_notebook.select(0)
        self.preview_notebook.select(0)
        self._add_log_message("تم الانتقال للصفحة الرئيسية", "INFO")

    def _show_settings_view(self):
        """عرض صفحة الإعدادات"""
        self.settings_notebook.select(1)
        self._add_log_message("تم الانتقال لصفحة الإعدادات", "INFO")

    def _show_results_view(self):
        """عرض صفحة النتائج"""
        self.preview_notebook.select(1)
        self._add_log_message("تم الانتقال لصفحة النتائج", "INFO")

    def _show_help_view(self):
        """عرض صفحة المساعدة"""
        self._show_help_dialog()

    def _quick_file_selection(self):
        """تحديد سريع للملفات"""
        directory = filedialog.askdirectory(title="اختر مجلد الصور")
        if directory:
            self.input_directory.set(directory)
            self._add_log_message(f"تم تحديد مجلد الإدخال: {directory}", "SUCCESS")

    def _new_project(self):
        """مشروع جديد"""
        # مسح الإعدادات الحالية
        self.input_directory.set("")
        self.output_directory.set("")
        self.config_file.set("")
        self._add_log_message("تم إنشاء مشروع جديد", "INFO")

    def _show_help_dialog(self):
        """عرض نظام المساعدة المتقدم"""
        if not hasattr(self, 'help_system'):
            self.help_system = HelpAndDocumentationSystem(self.root, self.theme)

        self.help_system.show_help_window()

    def _create_enhanced_basic_settings(self, parent):
        """إنشاء الإعدادات الأساسية المحسنة"""
        # إطار التمرير
        canvas = tk.Canvas(parent, bg=self.theme.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إعدادات المجلدات
        folders_frame = ttk.LabelFrame(scrollable_frame, text="📁 إعدادات المجلدات",
                                      style='Card.TLabelFrame', padding="15")
        folders_frame.pack(fill=tk.X, padx=10, pady=10)

        # مجلد الإدخال
        input_frame = ttk.Frame(folders_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="مجلد الصور:", style='Body.TLabel').pack(anchor=tk.W)

        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill=tk.X, pady=(5, 0))

        self.input_entry = ttk.Entry(input_entry_frame, textvariable=self.input_directory,
                                    font=self.theme.fonts['body'])
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        input_browse_btn = ttk.Button(input_entry_frame, text="📁 تصفح",
                                     command=self._browse_input_directory,
                                     style='Primary.TButton')
        input_browse_btn.pack(side=tk.RIGHT)

        # إضافة تلميحات
        add_tooltip(self.input_entry, "حدد المجلد الذي يحتوي على الصور المراد معالجتها")
        add_tooltip(input_browse_btn, "تصفح واختيار مجلد الصور")

        # مجلد الإخراج
        output_frame = ttk.Frame(folders_frame)
        output_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(output_frame, text="مجلد الإخراج:", style='Body.TLabel').pack(anchor=tk.W)

        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill=tk.X, pady=(5, 0))

        self.output_entry = ttk.Entry(output_entry_frame, textvariable=self.output_directory,
                                     font=self.theme.fonts['body'])
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        output_browse_btn = ttk.Button(output_entry_frame, text="📁 تصفح",
                                      command=self._browse_output_directory,
                                      style='Primary.TButton')
        output_browse_btn.pack(side=tk.RIGHT)

        add_tooltip(self.output_entry, "حدد المجلد لحفظ النتائج والتقارير")
        add_tooltip(output_browse_btn, "تصفح واختيار مجلد الإخراج")

        # ملف التكوين
        config_frame = ttk.Frame(folders_frame)
        config_frame.pack(fill=tk.X)

        ttk.Label(config_frame, text="ملف التكوين:", style='Body.TLabel').pack(anchor=tk.W)

        config_entry_frame = ttk.Frame(config_frame)
        config_entry_frame.pack(fill=tk.X, pady=(5, 0))

        self.config_entry = ttk.Entry(config_entry_frame, textvariable=self.config_file,
                                     font=self.theme.fonts['body'])
        self.config_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        config_browse_btn = ttk.Button(config_entry_frame, text="📄 تصفح",
                                      command=self._browse_config_file,
                                      style='Primary.TButton')
        config_browse_btn.pack(side=tk.RIGHT)

        add_tooltip(self.config_entry, "ملف التكوين للإعدادات المتقدمة (اختياري)")
        add_tooltip(config_browse_btn, "تصفح واختيار ملف التكوين")

        # إعدادات المعالجة السريعة
        quick_frame = ttk.LabelFrame(scrollable_frame, text="⚡ إعدادات سريعة",
                                    style='Card.TLabelFrame', padding="15")
        quick_frame.pack(fill=tk.X, padx=10, pady=10)

        # أحجام الصور المحددة مسبقاً
        size_frame = ttk.Frame(quick_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="حجم الصورة المستهدف:", style='Body.TLabel').pack(anchor=tk.W)

        size_buttons_frame = ttk.Frame(size_frame)
        size_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        size_presets = [
            ("256×256", 256, 256),
            ("512×512", 512, 512),
            ("1024×1024", 1024, 1024),
            ("مخصص", None, None)
        ]

        self.size_preset_var = tk.StringVar(value="512×512")

        for text, width, height in size_presets:
            btn = ttk.Radiobutton(size_buttons_frame, text=text,
                                 variable=self.size_preset_var, value=text,
                                 command=lambda w=width, h=height: self._set_image_size(w, h))
            btn.pack(side=tk.LEFT, padx=(0, 10))
            add_tooltip(btn, f"تعيين حجم الصورة إلى {text}")

        # حقول الحجم المخصص
        custom_size_frame = ttk.Frame(quick_frame)
        custom_size_frame.pack(fill=tk.X)

        ttk.Label(custom_size_frame, text="الحجم المخصص:", style='Body.TLabel').pack(anchor=tk.W)

        size_inputs_frame = ttk.Frame(custom_size_frame)
        size_inputs_frame.pack(anchor=tk.W, pady=(5, 0))

        ttk.Label(size_inputs_frame, text="العرض:", style='Body.TLabel').pack(side=tk.LEFT)
        width_entry = ttk.Entry(size_inputs_frame, textvariable=self.target_size_width, width=8)
        width_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(size_inputs_frame, text="الارتفاع:", style='Body.TLabel').pack(side=tk.LEFT)
        height_entry = ttk.Entry(size_inputs_frame, textvariable=self.target_size_height, width=8)
        height_entry.pack(side=tk.LEFT, padx=(5, 0))

        add_tooltip(width_entry, "عرض الصورة بالبكسل")
        add_tooltip(height_entry, "ارتفاع الصورة بالبكسل")

        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _set_image_size(self, width, height):
        """تعيين حجم الصورة"""
        if width and height:
            self.target_size_width.set(width)
            self.target_size_height.set(height)

    def _browse_input_directory(self):
        """تصفح مجلد الإدخال"""
        directory = filedialog.askdirectory(title="اختر مجلد الصور")
        if directory:
            self.input_directory.set(directory)
            self._add_log_message(f"تم تحديد مجلد الإدخال: {directory}", "SUCCESS")

    def _browse_output_directory(self):
        """تصفح مجلد الإخراج"""
        directory = filedialog.askdirectory(title="اختر مجلد الإخراج")
        if directory:
            self.output_directory.set(directory)
            self._add_log_message(f"تم تحديد مجلد الإخراج: {directory}", "SUCCESS")

    def _browse_config_file(self):
        """تصفح ملف التكوين"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف التكوين",
            filetypes=[
                ("ملفات YAML", "*.yaml *.yml"),
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )
        if file_path:
            self.config_file.set(file_path)
            self._add_log_message(f"تم تحديد ملف التكوين: {file_path}", "SUCCESS")

    def _create_enhanced_advanced_settings(self, parent):
        """إنشاء الإعدادات المتقدمة المحسنة"""
        # إطار التمرير
        canvas = tk.Canvas(parent, bg=self.theme.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إعدادات المعالجة
        processing_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ إعدادات المعالجة",
                                         style='Card.TLabelFrame', padding="15")
        processing_frame.pack(fill=tk.X, padx=10, pady=10)

        # حجم الدفعة
        batch_frame = ttk.Frame(processing_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(batch_frame, text="حجم الدفعة:", style='Body.TLabel').pack(side=tk.LEFT)
        batch_scale = ttk.Scale(batch_frame, from_=1, to=64, variable=self.batch_size,
                               orient=tk.HORIZONTAL, length=200)
        batch_scale.pack(side=tk.LEFT, padx=(10, 5))

        self.batch_label = ttk.Label(batch_frame, text="16", style='Muted.TLabel')
        self.batch_label.pack(side=tk.LEFT)

        # تحديث تسمية حجم الدفعة
        def update_batch_label(val):
            self.batch_label.config(text=str(int(float(val))))

        batch_scale.configure(command=update_batch_label)
        add_tooltip(batch_scale, "عدد الصور التي تتم معالجتها في كل دفعة")

        # خيارات التحليل
        analysis_frame = ttk.LabelFrame(scrollable_frame, text="📊 خيارات التحليل",
                                       style='Card.TLabelFrame', padding="15")
        analysis_frame.pack(fill=tk.X, padx=10, pady=10)

        # تمكين التحليلات
        analytics_check = ttk.Checkbutton(analysis_frame, text="تمكين التحليلات المتقدمة",
                                         variable=self.enable_analytics)
        analytics_check.pack(anchor=tk.W, pady=(0, 5))
        add_tooltip(analytics_check, "تمكين تحليل البيئات والإحصائيات المتقدمة")

        # تصدير YOLO
        yolo_check = ttk.Checkbutton(analysis_frame, text="تصدير تنسيق YOLO",
                                    variable=self.enable_yolo_export)
        yolo_check.pack(anchor=tk.W, pady=(0, 5))
        add_tooltip(yolo_check, "تصدير النتائج بتنسيق YOLO للتدريب")

        # الوضع المفصل
        verbose_check = ttk.Checkbutton(analysis_frame, text="الوضع المفصل",
                                       variable=self.verbose_mode)
        verbose_check.pack(anchor=tk.W)
        add_tooltip(verbose_check, "عرض تفاصيل إضافية أثناء المعالجة")

        # إعدادات الأداء
        performance_frame = ttk.LabelFrame(scrollable_frame, text="🚀 إعدادات الأداء",
                                          style='Card.TLabelFrame', padding="15")
        performance_frame.pack(fill=tk.X, padx=10, pady=10)

        # عدد المعالجات
        cpu_frame = ttk.Frame(performance_frame)
        cpu_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(cpu_frame, text="عدد المعالجات:", style='Body.TLabel').pack(side=tk.LEFT)

        self.cpu_count_var = tk.IntVar(value=4)
        cpu_scale = ttk.Scale(cpu_frame, from_=1, to=16, variable=self.cpu_count_var,
                             orient=tk.HORIZONTAL, length=200)
        cpu_scale.pack(side=tk.LEFT, padx=(10, 5))

        self.cpu_label = ttk.Label(cpu_frame, text="4", style='Muted.TLabel')
        self.cpu_label.pack(side=tk.LEFT)

        def update_cpu_label(val):
            self.cpu_label.config(text=str(int(float(val))))

        cpu_scale.configure(command=update_cpu_label)
        add_tooltip(cpu_scale, "عدد المعالجات المستخدمة في المعالجة المتوازية")

        # استخدام GPU
        self.use_gpu_var = tk.BooleanVar(value=False)
        gpu_check = ttk.Checkbutton(performance_frame, text="استخدام GPU (إذا كان متوفراً)",
                                   variable=self.use_gpu_var)
        gpu_check.pack(anchor=tk.W)
        add_tooltip(gpu_check, "استخدام كرت الرسوميات لتسريع المعالجة")

        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_enhanced_control_panel(self, parent):
        """إنشاء لوحة التحكم المحسنة"""
        # أزرار التحكم الرئيسية
        main_buttons_frame = ttk.LabelFrame(parent, text="🎮 العمليات الرئيسية",
                                           style='Card.TLabelFrame', padding="15")
        main_buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # زر البدء
        self.start_button = ttk.Button(main_buttons_frame, text="🚀 بدء المعالجة",
                                      command=self._start_processing,
                                      style='Success.TButton')
        self.start_button.pack(fill=tk.X, pady=(0, 5))
        add_tooltip(self.start_button, "بدء معالجة الصور (F5)")

        # زر الإيقاف
        self.stop_button = ttk.Button(main_buttons_frame, text="⏹️ إيقاف المعالجة",
                                     command=self._stop_processing,
                                     state=tk.DISABLED,
                                     style='Danger.TButton')
        self.stop_button.pack(fill=tk.X, pady=(0, 5))
        add_tooltip(self.stop_button, "إيقاف المعالجة الحالية (Esc)")

        # زر الإيقاف المؤقت
        self.pause_button = ttk.Button(main_buttons_frame, text="⏸️ إيقاف مؤقت",
                                      command=self._pause_processing,
                                      state=tk.DISABLED,
                                      style='Warning.TButton')
        self.pause_button.pack(fill=tk.X)
        add_tooltip(self.pause_button, "إيقاف مؤقت للمعالجة")

        # أزرار إضافية
        additional_buttons_frame = ttk.LabelFrame(parent, text="🔧 عمليات إضافية",
                                                 style='Card.TLabelFrame', padding="15")
        additional_buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # أزرار في صفوف
        row1_frame = ttk.Frame(additional_buttons_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))

        analytics_btn = ttk.Button(row1_frame, text="📊 عرض التحليلات",
                                  command=self._show_analytics)
        analytics_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        add_tooltip(analytics_btn, "عرض التحليلات والإحصائيات")

        yolo_btn = ttk.Button(row1_frame, text="🎯 تدريب YOLO",
                             command=self._launch_yolo_training)
        yolo_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))
        add_tooltip(yolo_btn, "فتح واجهة تدريب YOLO")

        row2_frame = ttk.Frame(additional_buttons_frame)
        row2_frame.pack(fill=tk.X)

        report_btn = ttk.Button(row2_frame, text="📋 إنشاء تقرير",
                               command=self._generate_report)
        report_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        add_tooltip(report_btn, "إنشاء تقرير شامل للنتائج")

        export_btn = ttk.Button(row2_frame, text="📤 تصدير النتائج",
                               command=self._export_results)
        export_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))
        add_tooltip(export_btn, "تصدير النتائج بصيغ مختلفة")

        # معلومات الحالة
        status_frame = ttk.LabelFrame(parent, text="📈 حالة النظام",
                                     style='Card.TLabelFrame', padding="15")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط تقدم مفصل
        progress_info_frame = ttk.Frame(status_frame)
        progress_info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(progress_info_frame, text="التقدم الإجمالي:", style='Body.TLabel').pack(anchor=tk.W)

        self.main_progress = ttk.Progressbar(progress_info_frame, mode='determinate',
                                           style='TProgressbar')
        self.main_progress.pack(fill=tk.X, pady=(5, 0))

        # معلومات مفصلة
        self.progress_info_var = tk.StringVar(value="في انتظار البدء...")
        progress_label = ttk.Label(progress_info_frame, textvariable=self.progress_info_var,
                                  style='Muted.TLabel')
        progress_label.pack(anchor=tk.W, pady=(5, 0))

        # منطقة السجل المصغر
        mini_log_frame = ttk.Frame(status_frame)
        mini_log_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(mini_log_frame, text="آخر العمليات:", style='Body.TLabel').pack(anchor=tk.W)

        self.mini_log_text = tk.Text(mini_log_frame, height=6, wrap=tk.WORD,
                                    font=self.theme.fonts['small'],
                                    bg=self.theme.colors['bg_secondary'],
                                    fg=self.theme.colors['text_secondary'],
                                    relief='flat', bd=1, state=tk.DISABLED)
        self.mini_log_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # إضافة رسالة ترحيب
        self._add_mini_log_message("مرحباً بك في تطبيق الإنقاذ والبحث المتقدم")
        self._add_mini_log_message("يرجى تحديد مجلد الصور ومجلد الإخراج للبدء")

    def _add_mini_log_message(self, message):
        """إضافة رسالة للسجل المصغر"""
        self.mini_log_text.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.mini_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.mini_log_text.see(tk.END)
        self.mini_log_text.config(state=tk.DISABLED)

    def _pause_processing(self):
        """إيقاف مؤقت للمعالجة"""
        # سيتم تنفيذها لاحقاً
        self._add_log_message("تم طلب الإيقاف المؤقت", "WARNING")
        pass

    def _create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # العنوان الرئيسي
        title_label = ttk.Label(header_frame,
                               text="🚁 تطبيق الإنقاذ والبحث المتقدم",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        # معلومات الإصدار
        version_label = ttk.Label(header_frame,
                                 text="الإصدار 2.0.0",
                                 style='small')
        version_label.pack(side=tk.RIGHT)

        # خط فاصل
        separator = ttk.Separator(parent, orient='horizontal')
        separator.pack(fill=tk.X, pady=(5, 0))

    def _create_left_panel(self, parent):
        """إنشاء اللوحة اليسرى للإعدادات"""
        left_frame = ttk.Frame(parent)

        # دفتر التبويبات للإعدادات
        settings_notebook = ttk.Notebook(left_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب الإعدادات الأساسية
        basic_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(basic_frame, text="الإعدادات الأساسية")
        self._create_basic_settings(basic_frame)

        # تبويب الإعدادات المتقدمة
        advanced_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(advanced_frame, text="الإعدادات المتقدمة")
        self._create_advanced_settings(advanced_frame)

        # تبويب التحكم
        control_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(control_frame, text="التحكم")
        self._create_control_panel(control_frame)

        return left_frame

    def _create_basic_settings(self, parent):
        """إنشاء الإعدادات الأساسية"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # قسم المسارات
        paths_frame = ttk.LabelFrame(scrollable_frame, text="المسارات", padding="10")
        paths_frame.pack(fill=tk.X, pady=(0, 10))

        # دليل الإدخال
        ttk.Label(paths_frame, text="دليل الصور المدخلة:").pack(anchor=tk.W)
        input_frame = ttk.Frame(paths_frame)
        input_frame.pack(fill=tk.X, pady=(2, 8))

        ttk.Entry(input_frame, textvariable=self.input_directory).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="تصفح",
                  command=self._browse_input_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # دليل الإخراج
        ttk.Label(paths_frame, text="دليل الإخراج:").pack(anchor=tk.W)
        output_frame = ttk.Frame(paths_frame)
        output_frame.pack(fill=tk.X, pady=(2, 8))

        ttk.Entry(output_frame, textvariable=self.output_directory).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="تصفح",
                  command=self._browse_output_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # ملف التكوين
        ttk.Label(paths_frame, text="ملف التكوين (اختياري):").pack(anchor=tk.W)
        config_frame = ttk.Frame(paths_frame)
        config_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Entry(config_frame, textvariable=self.config_file).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(config_frame, text="تصفح",
                  command=self._browse_config_file).pack(side=tk.RIGHT, padx=(5, 0))

        # قسم إعدادات المعالجة
        processing_frame = ttk.LabelFrame(scrollable_frame, text="إعدادات المعالجة", padding="10")
        processing_frame.pack(fill=tk.X, pady=(0, 10))

        # الحجم المستهدف
        size_frame = ttk.Frame(processing_frame)
        size_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(size_frame, text="الحجم المستهدف:").pack(side=tk.LEFT)
        ttk.Spinbox(size_frame, from_=128, to=2048, textvariable=self.target_size_width,
                   width=8).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(size_frame, text="×").pack(side=tk.LEFT)
        ttk.Spinbox(size_frame, from_=128, to=2048, textvariable=self.target_size_height,
                   width=8).pack(side=tk.LEFT, padx=(5, 0))

        # حجم الدفعة
        batch_frame = ttk.Frame(processing_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(batch_frame, text="حجم الدفعة:").pack(side=tk.LEFT)
        ttk.Spinbox(batch_frame, from_=1, to=128, textvariable=self.batch_size,
                   width=8).pack(side=tk.LEFT, padx=(10, 0))

        # خيارات إضافية
        options_frame = ttk.LabelFrame(scrollable_frame, text="خيارات إضافية", padding="10")
        options_frame.pack(fill=tk.X)

        ttk.Checkbutton(options_frame, text="تفعيل التحليلات المتقدمة",
                       variable=self.enable_analytics).pack(anchor=tk.W, pady=2)

        ttk.Checkbutton(options_frame, text="تصدير مجموعة بيانات YOLO",
                       variable=self.enable_yolo_export).pack(anchor=tk.W, pady=2)

        ttk.Checkbutton(options_frame, text="الوضع المفصل",
                       variable=self.verbose_mode).pack(anchor=tk.W, pady=2)

        # تعبئة الإطار القابل للتمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_advanced_settings(self, parent):
        """إنشاء الإعدادات المتقدمة"""
        # إطار للإعدادات المتقدمة
        advanced_frame = ttk.Frame(parent, padding="10")
        advanced_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة تطوير
        info_label = ttk.Label(advanced_frame,
                              text="الإعدادات المتقدمة ستكون متوفرة في الإصدارات القادمة",
                              style='info')
        info_label.pack(expand=True)

        # قائمة بالميزات المستقبلية
        features_text = """
الميزات المخططة:
• إعدادات نماذج الذكاء الاصطناعي
• تخصيص خوارزميات المعالجة
• إعدادات الأداء المتقدمة
• تكوين البيانات الجغرافية
• إعدادات التصدير المخصصة
        """

        features_label = ttk.Label(advanced_frame, text=features_text, justify=tk.LEFT)
        features_label.pack(pady=(20, 0))

    def _create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.Frame(parent, padding="10")
        control_frame.pack(fill=tk.BOTH, expand=True)

        # أزرار التحكم الرئيسية
        main_buttons_frame = ttk.LabelFrame(control_frame, text="العمليات الرئيسية", padding="10")
        main_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.start_button = ttk.Button(main_buttons_frame, text="🚀 بدء المعالجة",
                                      command=self._start_processing,
                                      style='Primary.TButton')
        self.start_button.pack(fill=tk.X, pady=(0, 5))

        self.stop_button = ttk.Button(main_buttons_frame, text="⏹️ إيقاف المعالجة",
                                     command=self._stop_processing,
                                     state=tk.DISABLED,
                                     style='Danger.TButton')
        self.stop_button.pack(fill=tk.X, pady=(0, 5))

        # أزرار إضافية
        additional_buttons_frame = ttk.LabelFrame(control_frame, text="عمليات إضافية", padding="10")
        additional_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(additional_buttons_frame, text="📊 عرض التحليلات",
                  command=self._show_analytics).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(additional_buttons_frame, text="🎯 تدريب YOLO",
                  command=self._launch_yolo_training).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(additional_buttons_frame, text="📋 إنشاء تقرير",
                  command=self._generate_report).pack(fill=tk.X, pady=(0, 5))

        # معلومات الحالة
        status_frame = ttk.LabelFrame(control_frame, text="حالة النظام", padding="10")
        status_frame.pack(fill=tk.X)

        self.status_text = tk.Text(status_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.pack(fill=tk.X)

        # إضافة رسالة ترحيب
        self._add_status_message("مرحباً بك في تطبيق الإنقاذ والبحث المتقدم")
        self._add_status_message("يرجى تحديد دليل الصور ودليل الإخراج للبدء")

    def _create_right_panel(self, parent):
        """إنشاء اللوحة اليمنى للمعاينة والنتائج"""
        right_frame = ttk.Frame(parent)

        # دفتر التبويبات للمعاينة والنتائج
        preview_notebook = ttk.Notebook(right_frame)
        preview_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب معاينة الصور
        preview_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(preview_frame, text="معاينة الصور")
        self.image_preview = ImagePreviewPanel(preview_frame)

        # تبويب النتائج
        results_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(results_frame, text="النتائج والتقارير")
        self.results_viewer = ResultsViewer(results_frame)

        return right_frame

    def _create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        # خط فاصل
        separator = ttk.Separator(status_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 5))

        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        self.info_var = tk.StringVar(value="")
        info_label = ttk.Label(status_frame, textvariable=self.info_var, foreground='gray')
        info_label.pack(side=tk.RIGHT)

    def _setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح مشروع...", command=self._open_project)
        file_menu.add_command(label="حفظ مشروع...", command=self._save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير النتائج...", command=self._export_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="تدريب YOLO...", command=self._launch_yolo_training)
        tools_menu.add_command(label="محرر التكوين...", command=self._open_config_editor)
        tools_menu.add_command(label="عارض السجلات...", command=self._open_log_viewer)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self._show_user_guide)
        help_menu.add_command(label="حول التطبيق", command=self._show_about)

    def _start_message_handler(self):
        """بدء معالج الرسائل"""
        self._process_message_queue()

    def _process_message_queue(self):
        """معالجة قائمة انتظار الرسائل"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self._handle_message(message)
        except queue.Empty:
            pass

        # جدولة المعالجة التالية
        self.root.after(100, self._process_message_queue)

    def _handle_message(self, message):
        """معالجة رسالة محسنة من قائمة الانتظار"""
        msg_type = message.get('type')

        if msg_type == 'progress':
            self._update_progress(message)
        elif msg_type == 'progress_update':
            self._handle_progress_update(message)
        elif msg_type == 'status':
            self._update_status(message.get('text', ''))
        elif msg_type == 'error':
            self._handle_error(message.get('error', ''))
        elif msg_type == 'processing_error':
            self._handle_processing_error_message(message)
        elif msg_type == 'complete':
            self._handle_completion(message.get('results'))
        elif msg_type == 'processing_complete':
            self._handle_processing_complete(message)
        elif msg_type == 'processing_cancelled':
            self._handle_processing_cancelled()

    def _handle_progress_update(self, message):
        """معالجة تحديث التقدم المحسن"""
        progress = message.get('progress', 0)
        status = message.get('status', '')
        processed = message.get('processed', 0)

        # تحديث شريط التقدم الرئيسي
        if hasattr(self, 'main_progress'):
            self.main_progress['value'] = progress

        # تحديث شريط التقدم المصغر
        if hasattr(self, 'mini_progress'):
            self.mini_progress['value'] = progress

        # تحديث معلومات التقدم
        if hasattr(self, 'progress_info_var'):
            if self.total_images_count > 0:
                self.progress_info_var.set(
                    f"{status} - {processed}/{self.total_images_count} صورة ({progress:.1f}%)"
                )
            else:
                self.progress_info_var.set(f"{status} - {progress:.1f}%")

        # تحديث الحالة العامة
        if hasattr(self, 'operation_var'):
            self.operation_var.set(status)

        # تحديث السجل المصغر
        if processed > self.processed_images_count:
            self._add_mini_log_message(f"تمت معالجة {processed} صورة")
            self.processed_images_count = processed

    def _handle_processing_error_message(self, message):
        """معالجة رسالة خطأ المعالجة"""
        error = message.get('error', 'خطأ غير معروف')

        # إظهار نافذة خطأ
        self._show_enhanced_error_dialog("خطأ في المعالجة", [error])

        # تحديث السجلات
        self._add_log_message(error, "ERROR")
        self._add_mini_log_message(f"خطأ: {error}")

        # إعادة تعيين الواجهة
        self._reset_ui_after_processing()

    def _handle_processing_complete(self, message):
        """معالجة اكتمال المعالجة بنجاح"""
        results = message.get('results', {})

        # إغلاق نافذة التقدم
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        # عرض نافذة النجاح
        self._show_success_dialog(results)

        # تحديث السجلات
        processing_time = results.get('processing_time', 0)
        processed_images = results.get('processed_images', 0)

        self._add_log_message(
            f"تمت المعالجة بنجاح - {processed_images} صورة في {processing_time:.1f} ثانية",
            "SUCCESS"
        )
        self._add_mini_log_message(f"اكتملت المعالجة بنجاح")

        # إعادة تعيين الواجهة
        self._reset_ui_after_processing()

        # عرض النتائج إذا كانت متوفرة
        if hasattr(self, 'results_viewer') and results:
            self.results_viewer.display_results(results)
            self.preview_notebook.select(1)  # التبديل لتبويب النتائج

    def _handle_processing_cancelled(self):
        """معالجة إلغاء المعالجة"""
        # إغلاق نافذة التقدم
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        # تحديث السجلات
        self._add_log_message("تم إلغاء المعالجة", "WARNING")
        self._add_mini_log_message("تم إلغاء المعالجة")

        # إعادة تعيين الواجهة
        self._reset_ui_after_processing()

    def _show_success_dialog(self, results):
        """عرض نافذة النجاح"""
        success_window = tk.Toplevel(self.root)
        success_window.title("تمت المعالجة بنجاح")
        success_window.geometry("500x400")
        success_window.configure(bg=self.theme.colors['bg_primary'])
        success_window.transient(self.root)
        success_window.grab_set()

        # العنوان
        header_frame = tk.Frame(success_window, bg=self.theme.colors['success'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="✅", font=('Arial', 32),
                bg=self.theme.colors['success'], fg=self.theme.colors['text_light']).pack(side=tk.LEFT, padx=20, pady=20)

        tk.Label(header_frame, text="تمت المعالجة بنجاح!", font=self.theme.fonts['heading_large'],
                bg=self.theme.colors['success'], fg=self.theme.colors['text_light']).pack(side=tk.LEFT, pady=20)

        # النتائج
        content_frame = tk.Frame(success_window, bg=self.theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # ملخص النتائج
        summary_text = self._create_results_summary(results)

        summary_widget = scrolledtext.ScrolledText(content_frame, height=12, wrap=tk.WORD,
                                                  font=self.theme.fonts['body'],
                                                  bg=self.theme.colors['bg_secondary'],
                                                  fg=self.theme.colors['text_primary'])
        summary_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        summary_widget.insert(1.0, summary_text)
        summary_widget.config(state=tk.DISABLED)

        # أزرار الإجراءات
        buttons_frame = tk.Frame(content_frame, bg=self.theme.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="عرض النتائج",
                  command=lambda: [success_window.destroy(), self._show_results_view()],
                  style='Primary.TButton').pack(side=tk.LEFT)

        ttk.Button(buttons_frame, text="فتح مجلد الإخراج",
                  command=lambda: self._open_output_folder(),
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(10, 0))

        ttk.Button(buttons_frame, text="موافق", command=success_window.destroy,
                  style='Success.TButton').pack(side=tk.RIGHT)

    def _create_results_summary(self, results):
        """إنشاء ملخص النتائج"""
        summary = []
        summary.append("🎉 تمت معالجة الصور بنجاح!")
        summary.append("")

        processed_images = results.get('processed_images', 0)
        processing_time = results.get('processing_time', 0)

        summary.append(f"📊 الإحصائيات:")
        summary.append(f"   • عدد الصور المعالجة: {processed_images}")
        summary.append(f"   • وقت المعالجة: {processing_time:.1f} ثانية")

        if processing_time > 0 and processed_images > 0:
            avg_time = processing_time / processed_images
            summary.append(f"   • متوسط الوقت لكل صورة: {avg_time:.2f} ثانية")

        summary.append("")
        summary.append(f"📁 النتائج محفوظة في:")
        summary.append(f"   {self.output_directory.get()}")

        return "\n".join(summary)

    def _open_output_folder(self):
        """فتح مجلد الإخراج"""
        import subprocess
        import platform

        output_path = self.output_directory.get()
        if output_path and Path(output_path).exists():
            try:
                if platform.system() == "Windows":
                    subprocess.run(["explorer", output_path])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", output_path])
                else:  # Linux
                    subprocess.run(["xdg-open", output_path])
            except Exception as e:
                self._add_log_message(f"فشل في فتح مجلد الإخراج: {e}", "ERROR")

    # معالجات الأحداث
    def _browse_input_directory(self):
        """تصفح دليل الإدخال"""
        directory = filedialog.askdirectory(title="اختر دليل الصور المدخلة")
        if directory:
            self.input_directory.set(directory)
            self._add_status_message(f"تم تحديد دليل الإدخال: {directory}")

    def _browse_output_directory(self):
        """تصفح دليل الإخراج"""
        directory = filedialog.askdirectory(title="اختر دليل الإخراج")
        if directory:
            self.output_directory.set(directory)
            self._add_status_message(f"تم تحديد دليل الإخراج: {directory}")

    def _browse_config_file(self):
        """تصفح ملف التكوين"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف التكوين",
            filetypes=[
                ("ملفات YAML", "*.yaml *.yml"),
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )
        if file_path:
            self.config_file.set(file_path)
            self._add_status_message(f"تم تحديد ملف التكوين: {file_path}")

    def _start_processing(self):
        """بدء معالجة الصور مع تحسينات التحقق والتغذية الراجعة"""
        try:
            # التحقق المحسن من المدخلات
            validation_result = self._validate_inputs()
            if not validation_result['valid']:
                self._show_enhanced_error_dialog("خطأ في الإعدادات", validation_result['errors'])
                return

            # عرض نافذة تأكيد مع ملخص الإعدادات
            if not self._show_processing_confirmation():
                return

            # إعداد المعاملات المحسنة
            processing_args = self._prepare_processing_args()

            # تحديث الواجهة مع تأثيرات بصرية
            self._update_ui_for_processing_start()

            # إنشاء نافذة التقدم المحسنة
            self.progress_dialog = ProgressDialog(self.root, "معالجة صور الإنقاذ والبحث", self.theme)

            # إعداد مراقبة الأداء
            self._setup_performance_monitoring()

            # بدء المعالجة في خيط منفصل
            self.processing_thread = threading.Thread(
                target=self._enhanced_processing_worker,
                args=(processing_args,),
                daemon=True
            )
            self.processing_thread.start()

            # تحديث السجلات والحالة
            self._add_log_message("بدء معالجة الصور...", "INFO")
            self._add_mini_log_message("بدء المعالجة...")
            self.status_indicator.config(text="● جاري المعالجة", fg=self.theme.colors['warning'])

        except Exception as e:
            self._handle_processing_error(f"خطأ في بدء المعالجة: {e}")

    def _validate_inputs(self):
        """التحقق المحسن من صحة المدخلات"""
        errors = []

        # فحص مجلد الإدخال
        if not self.input_directory.get():
            errors.append("• يرجى تحديد مجلد الصور")
        elif not Path(self.input_directory.get()).exists():
            errors.append("• مجلد الصور غير موجود")
        elif not any(Path(self.input_directory.get()).glob("*")):
            errors.append("• مجلد الصور فارغ")

        # فحص مجلد الإخراج
        if not self.output_directory.get():
            errors.append("• يرجى تحديد مجلد الإخراج")
        else:
            try:
                Path(self.output_directory.get()).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"• لا يمكن إنشاء مجلد الإخراج: {e}")

        # فحص ملف التكوين (إذا تم تحديده)
        if self.config_file.get() and not Path(self.config_file.get()).exists():
            errors.append("• ملف التكوين غير موجود")

        # فحص الإعدادات
        if self.target_size_width.get() <= 0 or self.target_size_height.get() <= 0:
            errors.append("• أبعاد الصورة يجب أن تكون أكبر من صفر")

        if self.batch_size.get() <= 0:
            errors.append("• حجم الدفعة يجب أن يكون أكبر من صفر")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    def _show_enhanced_error_dialog(self, title, errors):
        """عرض نافذة خطأ محسنة"""
        error_window = tk.Toplevel(self.root)
        error_window.title(title)
        error_window.geometry("500x400")
        error_window.configure(bg=self.theme.colors['bg_primary'])
        error_window.transient(self.root)
        error_window.grab_set()

        # أيقونة الخطأ والعنوان
        header_frame = tk.Frame(error_window, bg=self.theme.colors['danger'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="⚠️", font=('Arial', 32),
                bg=self.theme.colors['danger'], fg=self.theme.colors['text_light']).pack(side=tk.LEFT, padx=20, pady=20)

        tk.Label(header_frame, text=title, font=self.theme.fonts['heading_large'],
                bg=self.theme.colors['danger'], fg=self.theme.colors['text_light']).pack(side=tk.LEFT, pady=20)

        # قائمة الأخطاء
        content_frame = tk.Frame(error_window, bg=self.theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(content_frame, text="يرجى إصلاح الأخطاء التالية:",
                font=self.theme.fonts['body'], bg=self.theme.colors['bg_primary'],
                fg=self.theme.colors['text_primary']).pack(anchor=tk.W, pady=(0, 10))

        error_text = scrolledtext.ScrolledText(content_frame, height=8, wrap=tk.WORD,
                                              font=self.theme.fonts['body'],
                                              bg=self.theme.colors['bg_secondary'],
                                              fg=self.theme.colors['text_primary'])
        error_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        error_text.insert(1.0, "\n".join(errors))
        error_text.config(state=tk.DISABLED)

        # زر الإغلاق
        ttk.Button(content_frame, text="موافق", command=error_window.destroy,
                  style='Primary.TButton').pack(pady=(10, 0))

    def _show_processing_confirmation(self):
        """عرض نافذة تأكيد المعالجة مع ملخص الإعدادات"""
        confirm_window = tk.Toplevel(self.root)
        confirm_window.title("تأكيد بدء المعالجة")
        confirm_window.geometry("600x500")
        confirm_window.configure(bg=self.theme.colors['bg_primary'])
        confirm_window.transient(self.root)
        confirm_window.grab_set()

        result = {'confirmed': False}

        # العنوان
        header_frame = tk.Frame(confirm_window, bg=self.theme.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🚀 تأكيد بدء المعالجة",
                font=self.theme.fonts['heading_large'],
                bg=self.theme.colors['primary'],
                fg=self.theme.colors['text_light']).pack(pady=15)

        # ملخص الإعدادات
        content_frame = tk.Frame(confirm_window, bg=self.theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(content_frame, text="ملخص الإعدادات:",
                font=self.theme.fonts['heading'], bg=self.theme.colors['bg_primary'],
                fg=self.theme.colors['text_primary']).pack(anchor=tk.W, pady=(0, 10))

        # إنشاء ملخص الإعدادات
        summary_text = self._create_settings_summary()

        summary_widget = scrolledtext.ScrolledText(content_frame, height=12, wrap=tk.WORD,
                                                  font=self.theme.fonts['body'],
                                                  bg=self.theme.colors['bg_secondary'],
                                                  fg=self.theme.colors['text_primary'])
        summary_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        summary_widget.insert(1.0, summary_text)
        summary_widget.config(state=tk.DISABLED)

        # أزرار التأكيد
        buttons_frame = tk.Frame(content_frame, bg=self.theme.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X)

        def confirm():
            result['confirmed'] = True
            confirm_window.destroy()

        def cancel():
            result['confirmed'] = False
            confirm_window.destroy()

        ttk.Button(buttons_frame, text="إلغاء", command=cancel,
                  style='Danger.TButton').pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(buttons_frame, text="بدء المعالجة", command=confirm,
                  style='Success.TButton').pack(side=tk.RIGHT)

        # انتظار النتيجة
        confirm_window.wait_window()
        return result['confirmed']

    def _create_settings_summary(self):
        """إنشاء ملخص الإعدادات"""
        summary = []
        summary.append("📁 إعدادات المجلدات:")
        summary.append(f"   • مجلد الإدخال: {self.input_directory.get()}")
        summary.append(f"   • مجلد الإخراج: {self.output_directory.get()}")
        if self.config_file.get():
            summary.append(f"   • ملف التكوين: {self.config_file.get()}")

        summary.append("\n🖼️ إعدادات الصور:")
        summary.append(f"   • الحجم المستهدف: {self.target_size_width.get()} × {self.target_size_height.get()}")
        summary.append(f"   • حجم الدفعة: {self.batch_size.get()}")

        summary.append("\n⚙️ إعدادات المعالجة:")
        summary.append(f"   • التحليلات المتقدمة: {'مفعل' if self.enable_analytics.get() else 'معطل'}")
        summary.append(f"   • تصدير YOLO: {'مفعل' if self.enable_yolo_export.get() else 'معطل'}")
        summary.append(f"   • الوضع المفصل: {'مفعل' if self.verbose_mode.get() else 'معطل'}")

        if hasattr(self, 'cpu_count_var'):
            summary.append(f"   • عدد المعالجات: {self.cpu_count_var.get()}")
        if hasattr(self, 'use_gpu_var'):
            summary.append(f"   • استخدام GPU: {'مفعل' if self.use_gpu_var.get() else 'معطل'}")

        # إحصائيات المجلد
        try:
            input_path = Path(self.input_directory.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png")) + \
                         list(input_path.glob("*.jpeg")) + list(input_path.glob("*.bmp"))
            summary.append(f"\n📊 إحصائيات:")
            summary.append(f"   • عدد الصور المكتشفة: {len(image_files)}")

            if image_files:
                total_size = sum(f.stat().st_size for f in image_files)
                size_mb = total_size / (1024 * 1024)
                summary.append(f"   • الحجم الإجمالي: {size_mb:.1f} MB")

                estimated_time = len(image_files) * 2  # تقدير 2 ثانية لكل صورة
                summary.append(f"   • الوقت المقدر: {estimated_time // 60} دقيقة و {estimated_time % 60} ثانية")
        except:
            pass

        return "\n".join(summary)

    def _prepare_processing_args(self):
        """إعداد معاملات المعالجة المحسنة"""
        args = {
            'input_directory': self.input_directory.get(),
            'output_directory': self.output_directory.get(),
            'config_file': self.config_file.get() if self.config_file.get() else None,
            'target_size': [self.target_size_width.get(), self.target_size_height.get()],
            'batch_size': self.batch_size.get(),
            'enable_analytics': self.enable_analytics.get(),
            'enable_yolo_export': self.enable_yolo_export.get(),
            'verbose': self.verbose_mode.get()
        }

        # إضافة إعدادات الأداء إذا كانت متوفرة
        if hasattr(self, 'cpu_count_var'):
            args['cpu_count'] = self.cpu_count_var.get()
        if hasattr(self, 'use_gpu_var'):
            args['use_gpu'] = self.use_gpu_var.get()

        return args

    def _update_ui_for_processing_start(self):
        """تحديث الواجهة عند بدء المعالجة"""
        # تعطيل الأزرار
        self.start_button.config(state=tk.DISABLED)
        if hasattr(self, 'stop_button'):
            self.stop_button.config(state=tk.NORMAL)
        if hasattr(self, 'pause_button'):
            self.pause_button.config(state=tk.NORMAL)

        # تحديث شريط الحالة
        self.status_var.set("جاري المعالجة...")
        if hasattr(self, 'operation_var'):
            self.operation_var.set("تحضير المعالجة...")

        # إظهار شريط التقدم المصغر
        if hasattr(self, 'mini_progress'):
            self.mini_progress.pack(side=tk.LEFT, padx=(10, 0))
            self.mini_progress['value'] = 0

    def _setup_performance_monitoring(self):
        """إعداد مراقبة الأداء"""
        self.processing_start_time = datetime.now()
        self.processed_images_count = 0
        self.total_images_count = 0

        # حساب العدد الإجمالي للصور
        try:
            input_path = Path(self.input_directory.get())
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            self.total_images_count = len([f for f in input_path.rglob("*")
                                         if f.suffix.lower() in image_extensions])
        except:
            self.total_images_count = 0

    def _enhanced_processing_worker(self, args):
        """عامل المعالجة المحسن (يعمل في خيط منفصل)"""
        try:
            self._add_log_message("بدء تحضير المعالجة...", "INFO")

            # محاكاة المعالجة (يجب استبدالها بالمعالجة الفعلية)
            self._simulate_processing(args)

        except Exception as e:
            self._handle_processing_error(f"خطأ في المعالجة: {e}")

    def _simulate_processing(self, args):
        """محاكاة المعالجة للاختبار"""
        import time

        # مراحل المعالجة
        stages = [
            ("تحضير البيانات", 10),
            ("تحميل النماذج", 15),
            ("معالجة الصور", 60),
            ("تحليل النتائج", 10),
            ("إنشاء التقارير", 5)
        ]

        total_progress = 0

        for stage_name, stage_duration in stages:
            self.progress_dialog.set_status(f"جاري {stage_name}...")
            self._add_log_message(f"بدء {stage_name}", "INFO")

            # محاكاة التقدم التدريجي
            for i in range(stage_duration):
                if self.progress_dialog.is_cancelled():
                    self._handle_processing_cancellation()
                    return

                progress = total_progress + (i / stage_duration) * (100 / len(stages))
                self.progress_dialog.update_progress(
                    progress,
                    f"جاري {stage_name}...",
                    f"خطوة {i+1} من {stage_duration}"
                )

                # تحديث الواجهة الرئيسية
                self.message_queue.put({
                    'type': 'progress_update',
                    'progress': progress,
                    'status': f"جاري {stage_name}...",
                    'processed': int(progress * self.total_images_count / 100)
                })

                time.sleep(0.1)  # محاكاة الوقت

            total_progress += 100 / len(stages)

        # إنهاء المعالجة بنجاح
        self.message_queue.put({
            'type': 'processing_complete',
            'results': {
                'processed_images': self.total_images_count,
                'processing_time': (datetime.now() - self.processing_start_time).total_seconds(),
                'success': True
            }
        })

    def _handle_processing_error(self, error_message):
        """معالجة أخطاء المعالجة"""
        self._add_log_message(error_message, "ERROR")

        self.message_queue.put({
            'type': 'processing_error',
            'error': error_message
        })

    def _handle_processing_cancellation(self):
        """معالجة إلغاء المعالجة"""
        self._add_log_message("تم إلغاء المعالجة بواسطة المستخدم", "WARNING")

        self.message_queue.put({
            'type': 'processing_cancelled'
        })

    def _stop_processing(self):
        """إيقاف المعالجة"""
        if self.progress_dialog:
            self.progress_dialog._cancel_operation()

        self._add_log_message("تم طلب إيقاف المعالجة", "WARNING")

        # تحديث الواجهة
        self._reset_ui_after_processing()

    def _reset_ui_after_processing(self):
        """إعادة تعيين الواجهة بعد انتهاء المعالجة"""
        # تمكين الأزرار
        self.start_button.config(state=tk.NORMAL)
        if hasattr(self, 'stop_button'):
            self.stop_button.config(state=tk.DISABLED)
        if hasattr(self, 'pause_button'):
            self.pause_button.config(state=tk.DISABLED)

        # تحديث الحالة
        self.status_var.set("جاهز")
        if hasattr(self, 'operation_var'):
            self.operation_var.set("")

        # إخفاء شريط التقدم المصغر
        if hasattr(self, 'mini_progress'):
            self.mini_progress.pack_forget()

        # تحديث مؤشر الحالة
        self.status_indicator.config(text="● جاهز", fg=self.theme.colors['success'])

        # إغلاق نافذة التقدم
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

    def _processing_worker(self, args):
        """عامل المعالجة (يعمل في خيط منفصل)"""
        try:
            # استيراد الوحدة الرئيسية
            from . import RescueImageProcessor

            # إنشاء معالج الصور
            processor = RescueImageProcessor(args.get('config_file'))

            # تعيين دالة التحديث
            processor.set_progress_callback(self._progress_callback)

            # بدء المعالجة
            results = processor.process_rescue_images(
                input_directory=args['input_directory'],
                output_directory=args['output_directory'],
                target_size=args['target_size'],
                batch_size=args['batch_size'],
                enable_analytics=args['enable_analytics'],
                enable_yolo_export=args['enable_yolo_export'],
                verbose=args['verbose']
            )

            # إرسال النتائج
            self.message_queue.put({
                'type': 'complete',
                'results': results
            })

        except Exception as e:
            self.message_queue.put({
                'type': 'error',
                'error': str(e)
            })

    def _progress_callback(self, percentage, operation="", details=""):
        """دالة تحديث التقدم"""
        self.message_queue.put({
            'type': 'progress',
            'percentage': percentage,
            'operation': operation,
            'details': details
        })

    def _update_progress(self, message):
        """تحديث نافذة التقدم"""
        if self.progress_dialog:
            self.progress_dialog.update_progress(
                message.get('percentage', 0),
                message.get('operation', ''),
                message.get('details', '')
            )

    def _stop_processing(self):
        """إيقاف المعالجة"""
        if self.progress_dialog:
            self.progress_dialog._cancel_operation()

        self._reset_ui_state()
        self._add_status_message("تم إيقاف المعالجة بواسطة المستخدم")

    def _handle_completion(self, results):
        """معالجة اكتمال العملية"""
        self._reset_ui_state()

        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        if results:
            # عرض النتائج
            self.results_viewer.display_results(results)

            # تحديث الحالة
            total_images = results.get('execution_summary', {}).get('total_images_found', 0)
            success_rate = results.get('execution_summary', {}).get('success_rate', 0)

            self.status_var.set(f"اكتملت المعالجة - {total_images} صورة - معدل النجاح: {success_rate:.1f}%")
            self._add_status_message("اكتملت معالجة الصور بنجاح!")

            messagebox.showinfo("نجح", "اكتملت معالجة الصور بنجاح!")
        else:
            self.status_var.set("فشلت المعالجة")
            self._add_status_message("فشلت معالجة الصور")

    def _handle_error(self, error):
        """معالجة الأخطاء"""
        self._reset_ui_state()

        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        self.status_var.set("حدث خطأ في المعالجة")
        self._add_status_message(f"خطأ: {error}")

        messagebox.showerror("خطأ", f"حدث خطأ في المعالجة:\n{error}")

    def _reset_ui_state(self):
        """إعادة تعيين حالة الواجهة"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.current_operation = None

    def _update_status(self, text):
        """تحديث نص الحالة"""
        self.status_var.set(text)

    def _add_status_message(self, message):
        """إضافة رسالة لنص الحالة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, formatted_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    # معالجات القوائم
    def _open_project(self):
        """فتح مشروع محفوظ"""
        file_path = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات المشروع", "*.json"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # تحميل الإعدادات
                self.input_directory.set(project_data.get('input_directory', ''))
                self.output_directory.set(project_data.get('output_directory', ''))
                self.config_file.set(project_data.get('config_file', ''))

                self._add_status_message(f"تم تحميل المشروع: {file_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {e}")

    def _save_project(self):
        """حفظ المشروع الحالي"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات المشروع", "*.json"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                project_data = {
                    'input_directory': self.input_directory.get(),
                    'output_directory': self.output_directory.get(),
                    'config_file': self.config_file.get(),
                    'target_size_width': self.target_size_width.get(),
                    'target_size_height': self.target_size_height.get(),
                    'batch_size': self.batch_size.get(),
                    'enable_analytics': self.enable_analytics.get(),
                    'enable_yolo_export': self.enable_yolo_export.get(),
                    'verbose_mode': self.verbose_mode.get(),
                    'saved_timestamp': datetime.now().isoformat()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, ensure_ascii=False, indent=2)

                self._add_status_message(f"تم حفظ المشروع: {file_path}")
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {e}")

    def _export_results(self):
        """تصدير النتائج"""
        if hasattr(self.results_viewer, 'current_results') and self.results_viewer.current_results:
            self.results_viewer._export_report()
        else:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")

    def _show_analytics(self):
        """عرض التحليلات"""
        messagebox.showinfo("التحليلات", "ميزة التحليلات ستكون متوفرة قريباً")

    def _launch_yolo_training(self):
        """تشغيل تدريب YOLO"""
        try:
            from .training_interface import YOLOTrainingGUI

            # إنشاء نافذة تدريب منفصلة
            training_window = tk.Toplevel(self.root)
            training_window.title("تدريب نماذج YOLO")
            training_window.geometry("800x600")

            # إنشاء واجهة التدريب
            training_gui = YOLOTrainingGUI()
            training_gui.root = training_window
            training_gui._setup_ui()

        except ImportError:
            messagebox.showerror("خطأ", "وحدة تدريب YOLO غير متوفرة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل تدريب YOLO: {e}")

    def _generate_report(self):
        """إنشاء تقرير"""
        messagebox.showinfo("التقرير", "ميزة إنشاء التقارير ستكون متوفرة قريباً")

    def _open_config_editor(self):
        """فتح محرر التكوين"""
        messagebox.showinfo("محرر التكوين", "محرر التكوين سيكون متوفراً قريباً")

    def _open_log_viewer(self):
        """فتح عارض السجلات"""
        messagebox.showinfo("عارض السجلات", "عارض السجلات سيكون متوفراً قريباً")

    def _show_user_guide(self):
        """عرض دليل المستخدم"""
        guide_text = """
دليل المستخدم السريع
===================

1. تحديد المسارات:
   - اختر دليل الصور المدخلة
   - اختر دليل الإخراج
   - (اختياري) حدد ملف التكوين

2. ضبط الإعدادات:
   - حدد الحجم المستهدف للصور
   - اختر حجم الدفعة المناسب
   - فعل الخيارات المطلوبة

3. بدء المعالجة:
   - اضغط على "بدء المعالجة"
   - راقب التقدم في النافذة المنبثقة
   - انتظر اكتمال العملية

4. مراجعة النتائج:
   - تصفح النتائج في تبويب "النتائج والتقارير"
   - صدر التقارير حسب الحاجة
        """

        guide_window = tk.Toplevel(self.root)
        guide_window.title("دليل المستخدم")
        guide_window.geometry("600x400")

        text_widget = scrolledtext.ScrolledText(guide_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, guide_text)
        text_widget.config(state=tk.DISABLED)

    def _show_about(self):
        """عرض معلومات التطبيق"""
        about_text = """
تطبيق الإنقاذ والبحث المتقدم
الإصدار 2.0.0

تطبيق متخصص لمعالجة وتصنيف صور الإنقاذ والبحث
باستخدام تقنيات الذكاء الاصطناعي المتقدمة.

الميزات:
• معالجة متقدمة للصور
• تصنيف ذكي للبيئات
• كشف أهداف الإنقاذ
• تصدير مجموعات بيانات YOLO
• تحليلات تكتيكية شاملة
• واجهة مستخدم محسنة

تم التطوير بواسطة فريق الذكاء الاصطناعي
للإنقاذ والبحث
        """

        messagebox.showinfo("حول التطبيق", about_text)

    def run(self):
        """تشغيل الواجهة الرسومية"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()


def main():
    """الدالة الرئيسية لتشغيل الواجهة"""
    try:
        app = EnhancedRescueGUI()
        app.run()
    except ImportError as e:
        print(f"خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات المطلوبة")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")


if __name__ == "__main__":
    main()