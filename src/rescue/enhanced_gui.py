"""
واجهة المستخدم الرسومية المحسنة للإنقاذ والبحث
Enhanced Graphical User Interface for Search and Rescue
======================================================

واجهة رسومية متقدمة ومتخصصة لتطبيق معالجة وتصنيف صور الإنقاذ والبحث
مع دعم اللغة العربية والميزات التفاعلية المتقدمة.
"""

import os
import sys
import json
import threading
import queue
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    from tkinter import font as tkFont
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from ..utils.exceptions import ApplicationError


class RescueGUITheme:
    """
    نظام الألوان والتصميم للواجهة
    Color scheme and design system for the interface
    """

    def __init__(self):
        """تهيئة نظام الألوان"""
        # ألوان أساسية للإنقاذ والبحث
        self.colors = {
            # ألوان رئيسية
            'primary': '#1e3a8a',      # أزرق داكن
            'secondary': '#dc2626',    # أحمر للطوارئ
            'success': '#16a34a',      # أخضر للنجاح
            'warning': '#d97706',      # برتقالي للتحذير
            'info': '#0891b2',         # أزرق فاتح للمعلومات
            'danger': '#dc2626',       # أحمر للخطر

            # ألوان الخلفية
            'bg_primary': '#f8fafc',   # خلفية رئيسية
            'bg_secondary': '#e2e8f0', # خلفية ثانوية
            'bg_dark': '#1e293b',      # خلفية داكنة

            # ألوان النص
            'text_primary': '#1e293b',
            'text_secondary': '#64748b',
            'text_light': '#ffffff',

            # ألوان البيئات
            'sea': '#0ea5e9',
            'desert': '#f59e0b',
            'coast': '#10b981',
            'urban': '#6b7280',
            'forest': '#22c55e',
            'mountain': '#8b5cf6'
        }

        # خطوط
        self.fonts = {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 14, 'bold'),
            'body': ('Arial', 11),
            'small': ('Arial', 9),
            'arabic': ('Tahoma', 11)
        }

    def configure_ttk_styles(self):
        """تكوين أنماط ttk"""
        style = ttk.Style()

        # تكوين الأنماط المخصصة
        style.configure('Title.TLabel',
                       font=self.fonts['title'],
                       foreground=self.colors['primary'])

        style.configure('Heading.TLabel',
                       font=self.fonts['heading'],
                       foreground=self.colors['text_primary'])

        style.configure('Success.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['success'])

        style.configure('Warning.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['warning'])

        style.configure('Danger.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['danger'])

        # أزرار مخصصة
        style.configure('Primary.TButton',
                       font=self.fonts['body'])

        style.configure('Success.TButton',
                       font=self.fonts['body'])

        style.configure('Danger.TButton',
                       font=self.fonts['body'])


class ProgressDialog:
    """
    نافذة تقدم العملية
    Progress dialog window
    """

    def __init__(self, parent, title="جاري المعالجة..."):
        """
        تهيئة نافذة التقدم

        Args:
            parent: النافذة الأب
            title: عنوان النافذة
        """
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)

        # جعل النافذة في المقدمة
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        self._setup_ui()

        # متغيرات التحكم
        self.cancelled = False

    def _setup_ui(self):
        """إعداد واجهة نافذة التقدم"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان العملية
        self.operation_label = ttk.Label(main_frame, text="جاري التحضير...",
                                        style='Heading.TLabel')
        self.operation_label.pack(pady=(0, 10))

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=300)
        self.progress_bar.pack(pady=(0, 10))

        # نص التقدم
        self.progress_text = ttk.Label(main_frame, text="0%")
        self.progress_text.pack(pady=(0, 10))

        # تفاصيل العملية
        self.details_text = scrolledtext.ScrolledText(main_frame, height=4, width=40)
        self.details_text.pack(pady=(0, 10), fill=tk.BOTH, expand=True)

        # أزرار التحكم
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        self.cancel_button = ttk.Button(button_frame, text="إلغاء",
                                       command=self._cancel_operation)
        self.cancel_button.pack(side=tk.RIGHT)

    def update_progress(self, percentage, operation="", details=""):
        """
        تحديث تقدم العملية

        Args:
            percentage: نسبة الإنجاز (0-100)
            operation: اسم العملية الحالية
            details: تفاصيل إضافية
        """
        self.progress_var.set(percentage)
        self.progress_text.config(text=f"{percentage:.1f}%")

        if operation:
            self.operation_label.config(text=operation)

        if details:
            self.details_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {details}\n")
            self.details_text.see(tk.END)

        self.dialog.update()

    def _cancel_operation(self):
        """إلغاء العملية"""
        self.cancelled = True
        self.cancel_button.config(state=tk.DISABLED, text="جاري الإلغاء...")

    def close(self):
        """إغلاق نافذة التقدم"""
        self.dialog.destroy()


class ImagePreviewPanel:
    """
    لوحة معاينة الصور
    Image preview panel
    """

    def __init__(self, parent):
        """
        تهيئة لوحة المعاينة

        Args:
            parent: الإطار الأب
        """
        self.parent = parent
        self.current_image_path = None
        self.current_image = None

        self._setup_ui()

    def _setup_ui(self):
        """إعداد واجهة لوحة المعاينة"""
        # إطار رئيسي
        self.main_frame = ttk.LabelFrame(self.parent, text="معاينة الصورة", padding="5")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # منطقة عرض الصورة
        self.image_frame = ttk.Frame(self.main_frame)
        self.image_frame.pack(fill=tk.BOTH, expand=True)

        # تسمية عرض الصورة
        self.image_label = ttk.Label(self.image_frame, text="لا توجد صورة محددة")
        self.image_label.pack(expand=True)

        # معلومات الصورة
        self.info_frame = ttk.Frame(self.main_frame)
        self.info_frame.pack(fill=tk.X, pady=(5, 0))

        self.info_text = tk.Text(self.info_frame, height=4, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X)

        # أزرار التحكم
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(self.control_frame, text="تحديد صورة",
                  command=self._select_image).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(self.control_frame, text="مسح",
                  command=self._clear_image).pack(side=tk.LEFT)

    def _select_image(self):
        """تحديد صورة للمعاينة"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[
                ("ملفات الصور", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.load_image(file_path)

    def load_image(self, image_path):
        """
        تحميل وعرض صورة

        Args:
            image_path: مسار الصورة
        """
        try:
            if not PIL_AVAILABLE:
                self.image_label.config(text="PIL غير متوفر لعرض الصور")
                return

            self.current_image_path = image_path

            # تحميل الصورة
            image = Image.open(image_path)
            self.current_image = image

            # تغيير حجم الصورة للعرض
            display_size = (300, 200)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(image)
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # الاحتفاظ بمرجع

            # عرض معلومات الصورة
            self._display_image_info()

        except Exception as e:
            self.image_label.config(text=f"خطأ في تحميل الصورة: {e}")
            self.info_text.delete(1.0, tk.END)

    def _display_image_info(self):
        """عرض معلومات الصورة"""
        if not self.current_image:
            return

        info = []
        info.append(f"المسار: {Path(self.current_image_path).name}")
        info.append(f"الأبعاد: {self.current_image.size[0]} × {self.current_image.size[1]}")
        info.append(f"التنسيق: {self.current_image.format}")
        info.append(f"النمط: {self.current_image.mode}")

        # حجم الملف
        file_size = Path(self.current_image_path).stat().st_size
        if file_size > 1024 * 1024:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        elif file_size > 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size} bytes"

        info.append(f"الحجم: {size_str}")

        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, "\n".join(info))

    def _clear_image(self):
        """مسح الصورة المعروضة"""
        self.current_image_path = None
        self.current_image = None
        self.image_label.config(image="", text="لا توجد صورة محددة")
        self.image_label.image = None
        self.info_text.delete(1.0, tk.END)


class ResultsViewer:
    """
    عارض النتائج والتقارير
    Results and reports viewer
    """

    def __init__(self, parent):
        """
        تهيئة عارض النتائج

        Args:
            parent: الإطار الأب
        """
        self.parent = parent
        self.current_results = None

        self._setup_ui()

    def _setup_ui(self):
        """إعداد واجهة عارض النتائج"""
        # إطار رئيسي
        self.main_frame = ttk.LabelFrame(self.parent, text="النتائج والتقارير", padding="5")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط أدوات
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(self.toolbar, text="تحميل نتائج",
                  command=self._load_results).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(self.toolbar, text="تصدير تقرير",
                  command=self._export_report).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(self.toolbar, text="مسح",
                  command=self._clear_results).pack(side=tk.LEFT)

        # منطقة عرض النتائج
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب الملخص
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="الملخص")

        self.summary_text = scrolledtext.ScrolledText(self.summary_frame)
        self.summary_text.pack(fill=tk.BOTH, expand=True)

        # تبويب التفاصيل
        self.details_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.details_frame, text="التفاصيل")

        self.details_tree = ttk.Treeview(self.details_frame, columns=('value',), show='tree headings')
        self.details_tree.heading('#0', text='المعيار')
        self.details_tree.heading('value', text='القيمة')
        self.details_tree.pack(fill=tk.BOTH, expand=True)

        # تبويب الرسوم البيانية
        if MATPLOTLIB_AVAILABLE:
            self.charts_frame = ttk.Frame(self.notebook)
            self.notebook.add(self.charts_frame, text="الرسوم البيانية")

            # إطار للرسم البياني
            self.chart_canvas_frame = ttk.Frame(self.charts_frame)
            self.chart_canvas_frame.pack(fill=tk.BOTH, expand=True)

    def _load_results(self):
        """تحميل ملف نتائج"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النتائج",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.load_results_file(file_path)

    def load_results_file(self, file_path):
        """
        تحميل ملف النتائج

        Args:
            file_path: مسار ملف النتائج
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.current_results = json.load(f)

            self._display_results()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل النتائج: {e}")

    def _display_results(self):
        """عرض النتائج المحملة"""
        if not self.current_results:
            return

        # عرض الملخص
        self._display_summary()

        # عرض التفاصيل
        self._display_details()

        # عرض الرسوم البيانية
        if MATPLOTLIB_AVAILABLE:
            self._display_charts()

    def _display_summary(self):
        """عرض ملخص النتائج"""
        self.summary_text.delete(1.0, tk.END)

        summary_lines = []

        # معلومات أساسية
        if 'execution_summary' in self.current_results:
            exec_summary = self.current_results['execution_summary']
            summary_lines.extend([
                "=== ملخص التنفيذ ===",
                f"وقت التنفيذ: {exec_summary.get('execution_time_formatted', 'غير محدد')}",
                f"إجمالي الصور: {exec_summary.get('total_images_found', 0)}",
                f"التصنيفات الناجحة: {exec_summary.get('successful_classifications', 0)}",
                f"معدل النجاح: {exec_summary.get('success_rate', 0):.1f}%",
                f"سرعة المعالجة: {exec_summary.get('processing_speed_images_per_second', 0):.2f} صورة/ثانية",
                ""
            ])

        # معلومات مجموعة البيانات
        if 'dataset_info' in self.current_results:
            dataset_info = self.current_results['dataset_info']
            summary_lines.extend([
                "=== معلومات مجموعة البيانات ===",
                f"دليل الإخراج: {dataset_info.get('output_directory', 'غير محدد')}",
                f"إجمالي الصور: {dataset_info.get('total_images', 0)}",
                ""
            ])

            if dataset_info.get('yolo_dataset'):
                summary_lines.extend([
                    "=== مجموعة بيانات YOLO ===",
                    f"ملف data.yaml: {dataset_info['yolo_dataset'].get('data_yaml', 'غير محدد')}",
                    ""
                ])

        self.summary_text.insert(1.0, "\n".join(summary_lines))

    def _display_details(self):
        """عرض تفاصيل النتائج"""
        # مسح البيانات السابقة
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        # إضافة البيانات الجديدة
        self._add_tree_data("النتائج", self.current_results, "")

    def _add_tree_data(self, name, data, parent):
        """إضافة بيانات للشجرة بشكل تكراري"""
        if isinstance(data, dict):
            node = self.details_tree.insert(parent, 'end', text=name, values=('',))
            for key, value in data.items():
                self._add_tree_data(key, value, node)
        elif isinstance(data, list):
            node = self.details_tree.insert(parent, 'end', text=f"{name} (قائمة)", values=(f"{len(data)} عنصر",))
            for i, item in enumerate(data[:10]):  # عرض أول 10 عناصر فقط
                self._add_tree_data(f"[{i}]", item, node)
            if len(data) > 10:
                self.details_tree.insert(node, 'end', text="...", values=(f"و {len(data) - 10} عنصر آخر",))
        else:
            self.details_tree.insert(parent, 'end', text=name, values=(str(data),))

    def _display_charts(self):
        """عرض الرسوم البيانية"""
        # مسح الرسوم السابقة
        for widget in self.chart_canvas_frame.winfo_children():
            widget.destroy()

        try:
            # إنشاء رسم بياني بسيط
            fig = Figure(figsize=(8, 6), dpi=100)

            # رسم توزيع البيئات إذا كان متوفراً
            if ('analysis_results' in self.current_results and
                'environment_analysis' in self.current_results['analysis_results']):

                env_data = self.current_results['analysis_results']['environment_analysis']
                if 'distribution_percentages' in env_data:
                    ax = fig.add_subplot(111)

                    environments = list(env_data['distribution_percentages'].keys())
                    percentages = list(env_data['distribution_percentages'].values())

                    ax.pie(percentages, labels=environments, autopct='%1.1f%%')
                    ax.set_title('توزيع البيئات')

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.chart_canvas_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            error_label = ttk.Label(self.chart_canvas_frame,
                                   text=f"خطأ في عرض الرسوم البيانية: {e}")
            error_label.pack(expand=True)

    def _export_report(self):
        """تصدير تقرير النتائج"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".txt",
            filetypes=[
                ("ملفات نصية", "*.txt"),
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.current_results, f, ensure_ascii=False, indent=2, default=str)
                else:
                    # تصدير كملف نصي
                    summary_content = self.summary_text.get(1.0, tk.END)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(summary_content)

                messagebox.showinfo("نجح", f"تم حفظ التقرير: {file_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {e}")

    def _clear_results(self):
        """مسح النتائج المعروضة"""
        self.current_results = None
        self.summary_text.delete(1.0, tk.END)

        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        if MATPLOTLIB_AVAILABLE:
            for widget in self.chart_canvas_frame.winfo_children():
                widget.destroy()

    def display_results(self, results):
        """
        عرض النتائج مباشرة

        Args:
            results: النتائج المراد عرضها
        """
        self.current_results = results
        self._display_results()


class EnhancedRescueGUI:
    """
    الواجهة الرسومية المحسنة لتطبيق الإنقاذ والبحث
    Enhanced GUI for Search and Rescue Application
    """

    def __init__(self):
        """تهيئة الواجهة الرسومية المحسنة"""
        if not GUI_AVAILABLE:
            raise ImportError("tkinter غير متوفر للواجهة الرسومية")

        # النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("تطبيق الإنقاذ والبحث المتقدم - Advanced Search & Rescue Application")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)

        # نظام الألوان والتصميم
        self.theme = RescueGUITheme()
        self.theme.configure_ttk_styles()

        # متغيرات الواجهة
        self.input_directory = tk.StringVar()
        self.output_directory = tk.StringVar()
        self.config_file = tk.StringVar()

        # متغيرات الإعدادات
        self.target_size_width = tk.IntVar(value=512)
        self.target_size_height = tk.IntVar(value=512)
        self.batch_size = tk.IntVar(value=16)
        self.enable_analytics = tk.BooleanVar(value=True)
        self.enable_yolo_export = tk.BooleanVar(value=True)
        self.verbose_mode = tk.BooleanVar(value=False)

        # حالة التطبيق
        self.processing_thread = None
        self.progress_dialog = None
        self.current_operation = None

        # قائمة انتظار الرسائل
        self.message_queue = queue.Queue()

        # إعداد الواجهة
        self._setup_ui()
        self._setup_menu()

        # بدء معالج الرسائل
        self._start_message_handler()

        self.logger = logging.getLogger(__name__)

    def _setup_ui(self):
        """إعداد عناصر الواجهة الرئيسية"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط العنوان
        self._create_header(main_frame)

        # الإطار الرئيسي للمحتوى
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # تقسيم الواجهة إلى أعمدة
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=2)
        content_frame.rowconfigure(0, weight=1)

        # العمود الأيسر - الإعدادات والتحكم
        left_panel = self._create_left_panel(content_frame)
        left_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # العمود الأيمن - المعاينة والنتائج
        right_panel = self._create_right_panel(content_frame)
        right_panel.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # شريط الحالة
        self._create_status_bar(main_frame)

    def _create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # العنوان الرئيسي
        title_label = ttk.Label(header_frame,
                               text="🚁 تطبيق الإنقاذ والبحث المتقدم",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        # معلومات الإصدار
        version_label = ttk.Label(header_frame,
                                 text="الإصدار 2.0.0",
                                 style='small')
        version_label.pack(side=tk.RIGHT)

        # خط فاصل
        separator = ttk.Separator(parent, orient='horizontal')
        separator.pack(fill=tk.X, pady=(5, 0))

    def _create_left_panel(self, parent):
        """إنشاء اللوحة اليسرى للإعدادات"""
        left_frame = ttk.Frame(parent)

        # دفتر التبويبات للإعدادات
        settings_notebook = ttk.Notebook(left_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب الإعدادات الأساسية
        basic_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(basic_frame, text="الإعدادات الأساسية")
        self._create_basic_settings(basic_frame)

        # تبويب الإعدادات المتقدمة
        advanced_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(advanced_frame, text="الإعدادات المتقدمة")
        self._create_advanced_settings(advanced_frame)

        # تبويب التحكم
        control_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(control_frame, text="التحكم")
        self._create_control_panel(control_frame)

        return left_frame

    def _create_basic_settings(self, parent):
        """إنشاء الإعدادات الأساسية"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # قسم المسارات
        paths_frame = ttk.LabelFrame(scrollable_frame, text="المسارات", padding="10")
        paths_frame.pack(fill=tk.X, pady=(0, 10))

        # دليل الإدخال
        ttk.Label(paths_frame, text="دليل الصور المدخلة:").pack(anchor=tk.W)
        input_frame = ttk.Frame(paths_frame)
        input_frame.pack(fill=tk.X, pady=(2, 8))

        ttk.Entry(input_frame, textvariable=self.input_directory).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="تصفح",
                  command=self._browse_input_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # دليل الإخراج
        ttk.Label(paths_frame, text="دليل الإخراج:").pack(anchor=tk.W)
        output_frame = ttk.Frame(paths_frame)
        output_frame.pack(fill=tk.X, pady=(2, 8))

        ttk.Entry(output_frame, textvariable=self.output_directory).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="تصفح",
                  command=self._browse_output_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # ملف التكوين
        ttk.Label(paths_frame, text="ملف التكوين (اختياري):").pack(anchor=tk.W)
        config_frame = ttk.Frame(paths_frame)
        config_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Entry(config_frame, textvariable=self.config_file).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(config_frame, text="تصفح",
                  command=self._browse_config_file).pack(side=tk.RIGHT, padx=(5, 0))

        # قسم إعدادات المعالجة
        processing_frame = ttk.LabelFrame(scrollable_frame, text="إعدادات المعالجة", padding="10")
        processing_frame.pack(fill=tk.X, pady=(0, 10))

        # الحجم المستهدف
        size_frame = ttk.Frame(processing_frame)
        size_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(size_frame, text="الحجم المستهدف:").pack(side=tk.LEFT)
        ttk.Spinbox(size_frame, from_=128, to=2048, textvariable=self.target_size_width,
                   width=8).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(size_frame, text="×").pack(side=tk.LEFT)
        ttk.Spinbox(size_frame, from_=128, to=2048, textvariable=self.target_size_height,
                   width=8).pack(side=tk.LEFT, padx=(5, 0))

        # حجم الدفعة
        batch_frame = ttk.Frame(processing_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(batch_frame, text="حجم الدفعة:").pack(side=tk.LEFT)
        ttk.Spinbox(batch_frame, from_=1, to=128, textvariable=self.batch_size,
                   width=8).pack(side=tk.LEFT, padx=(10, 0))

        # خيارات إضافية
        options_frame = ttk.LabelFrame(scrollable_frame, text="خيارات إضافية", padding="10")
        options_frame.pack(fill=tk.X)

        ttk.Checkbutton(options_frame, text="تفعيل التحليلات المتقدمة",
                       variable=self.enable_analytics).pack(anchor=tk.W, pady=2)

        ttk.Checkbutton(options_frame, text="تصدير مجموعة بيانات YOLO",
                       variable=self.enable_yolo_export).pack(anchor=tk.W, pady=2)

        ttk.Checkbutton(options_frame, text="الوضع المفصل",
                       variable=self.verbose_mode).pack(anchor=tk.W, pady=2)

        # تعبئة الإطار القابل للتمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_advanced_settings(self, parent):
        """إنشاء الإعدادات المتقدمة"""
        # إطار للإعدادات المتقدمة
        advanced_frame = ttk.Frame(parent, padding="10")
        advanced_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة تطوير
        info_label = ttk.Label(advanced_frame,
                              text="الإعدادات المتقدمة ستكون متوفرة في الإصدارات القادمة",
                              style='info')
        info_label.pack(expand=True)

        # قائمة بالميزات المستقبلية
        features_text = """
الميزات المخططة:
• إعدادات نماذج الذكاء الاصطناعي
• تخصيص خوارزميات المعالجة
• إعدادات الأداء المتقدمة
• تكوين البيانات الجغرافية
• إعدادات التصدير المخصصة
        """

        features_label = ttk.Label(advanced_frame, text=features_text, justify=tk.LEFT)
        features_label.pack(pady=(20, 0))

    def _create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.Frame(parent, padding="10")
        control_frame.pack(fill=tk.BOTH, expand=True)

        # أزرار التحكم الرئيسية
        main_buttons_frame = ttk.LabelFrame(control_frame, text="العمليات الرئيسية", padding="10")
        main_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.start_button = ttk.Button(main_buttons_frame, text="🚀 بدء المعالجة",
                                      command=self._start_processing,
                                      style='Primary.TButton')
        self.start_button.pack(fill=tk.X, pady=(0, 5))

        self.stop_button = ttk.Button(main_buttons_frame, text="⏹️ إيقاف المعالجة",
                                     command=self._stop_processing,
                                     state=tk.DISABLED,
                                     style='Danger.TButton')
        self.stop_button.pack(fill=tk.X, pady=(0, 5))

        # أزرار إضافية
        additional_buttons_frame = ttk.LabelFrame(control_frame, text="عمليات إضافية", padding="10")
        additional_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(additional_buttons_frame, text="📊 عرض التحليلات",
                  command=self._show_analytics).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(additional_buttons_frame, text="🎯 تدريب YOLO",
                  command=self._launch_yolo_training).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(additional_buttons_frame, text="📋 إنشاء تقرير",
                  command=self._generate_report).pack(fill=tk.X, pady=(0, 5))

        # معلومات الحالة
        status_frame = ttk.LabelFrame(control_frame, text="حالة النظام", padding="10")
        status_frame.pack(fill=tk.X)

        self.status_text = tk.Text(status_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.pack(fill=tk.X)

        # إضافة رسالة ترحيب
        self._add_status_message("مرحباً بك في تطبيق الإنقاذ والبحث المتقدم")
        self._add_status_message("يرجى تحديد دليل الصور ودليل الإخراج للبدء")

    def _create_right_panel(self, parent):
        """إنشاء اللوحة اليمنى للمعاينة والنتائج"""
        right_frame = ttk.Frame(parent)

        # دفتر التبويبات للمعاينة والنتائج
        preview_notebook = ttk.Notebook(right_frame)
        preview_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب معاينة الصور
        preview_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(preview_frame, text="معاينة الصور")
        self.image_preview = ImagePreviewPanel(preview_frame)

        # تبويب النتائج
        results_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(results_frame, text="النتائج والتقارير")
        self.results_viewer = ResultsViewer(results_frame)

        return right_frame

    def _create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        # خط فاصل
        separator = ttk.Separator(status_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 5))

        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        self.info_var = tk.StringVar(value="")
        info_label = ttk.Label(status_frame, textvariable=self.info_var, foreground='gray')
        info_label.pack(side=tk.RIGHT)

    def _setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح مشروع...", command=self._open_project)
        file_menu.add_command(label="حفظ مشروع...", command=self._save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير النتائج...", command=self._export_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="تدريب YOLO...", command=self._launch_yolo_training)
        tools_menu.add_command(label="محرر التكوين...", command=self._open_config_editor)
        tools_menu.add_command(label="عارض السجلات...", command=self._open_log_viewer)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self._show_user_guide)
        help_menu.add_command(label="حول التطبيق", command=self._show_about)

    def _start_message_handler(self):
        """بدء معالج الرسائل"""
        self._process_message_queue()

    def _process_message_queue(self):
        """معالجة قائمة انتظار الرسائل"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self._handle_message(message)
        except queue.Empty:
            pass

        # جدولة المعالجة التالية
        self.root.after(100, self._process_message_queue)

    def _handle_message(self, message):
        """معالجة رسالة من قائمة الانتظار"""
        msg_type = message.get('type')

        if msg_type == 'progress':
            self._update_progress(message)
        elif msg_type == 'status':
            self._update_status(message.get('text', ''))
        elif msg_type == 'error':
            self._handle_error(message.get('error', ''))
        elif msg_type == 'complete':
            self._handle_completion(message.get('results'))

    # معالجات الأحداث
    def _browse_input_directory(self):
        """تصفح دليل الإدخال"""
        directory = filedialog.askdirectory(title="اختر دليل الصور المدخلة")
        if directory:
            self.input_directory.set(directory)
            self._add_status_message(f"تم تحديد دليل الإدخال: {directory}")

    def _browse_output_directory(self):
        """تصفح دليل الإخراج"""
        directory = filedialog.askdirectory(title="اختر دليل الإخراج")
        if directory:
            self.output_directory.set(directory)
            self._add_status_message(f"تم تحديد دليل الإخراج: {directory}")

    def _browse_config_file(self):
        """تصفح ملف التكوين"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف التكوين",
            filetypes=[
                ("ملفات YAML", "*.yaml *.yml"),
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )
        if file_path:
            self.config_file.set(file_path)
            self._add_status_message(f"تم تحديد ملف التكوين: {file_path}")

    def _start_processing(self):
        """بدء معالجة الصور"""
        # التحقق من المدخلات
        if not self.input_directory.get():
            messagebox.showerror("خطأ", "يرجى تحديد دليل الصور المدخلة")
            return

        if not self.output_directory.get():
            messagebox.showerror("خطأ", "يرجى تحديد دليل الإخراج")
            return

        if not Path(self.input_directory.get()).exists():
            messagebox.showerror("خطأ", "دليل الإدخال غير موجود")
            return

        # إعداد المعاملات
        processing_args = {
            'input_directory': self.input_directory.get(),
            'output_directory': self.output_directory.get(),
            'config_file': self.config_file.get() if self.config_file.get() else None,
            'target_size': [self.target_size_width.get(), self.target_size_height.get()],
            'batch_size': self.batch_size.get(),
            'enable_analytics': self.enable_analytics.get(),
            'enable_yolo_export': self.enable_yolo_export.get(),
            'verbose': self.verbose_mode.get()
        }

        # تحديث الواجهة
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_var.set("جاري المعالجة...")

        # إنشاء نافذة التقدم
        self.progress_dialog = ProgressDialog(self.root, "معالجة صور الإنقاذ والبحث")

        # بدء المعالجة في خيط منفصل
        self.processing_thread = threading.Thread(
            target=self._processing_worker,
            args=(processing_args,),
            daemon=True
        )
        self.processing_thread.start()

        self._add_status_message("بدء معالجة الصور...")

    def _processing_worker(self, args):
        """عامل المعالجة (يعمل في خيط منفصل)"""
        try:
            # استيراد الوحدة الرئيسية
            from . import RescueImageProcessor

            # إنشاء معالج الصور
            processor = RescueImageProcessor(args.get('config_file'))

            # تعيين دالة التحديث
            processor.set_progress_callback(self._progress_callback)

            # بدء المعالجة
            results = processor.process_rescue_images(
                input_directory=args['input_directory'],
                output_directory=args['output_directory'],
                target_size=args['target_size'],
                batch_size=args['batch_size'],
                enable_analytics=args['enable_analytics'],
                enable_yolo_export=args['enable_yolo_export'],
                verbose=args['verbose']
            )

            # إرسال النتائج
            self.message_queue.put({
                'type': 'complete',
                'results': results
            })

        except Exception as e:
            self.message_queue.put({
                'type': 'error',
                'error': str(e)
            })

    def _progress_callback(self, percentage, operation="", details=""):
        """دالة تحديث التقدم"""
        self.message_queue.put({
            'type': 'progress',
            'percentage': percentage,
            'operation': operation,
            'details': details
        })

    def _update_progress(self, message):
        """تحديث نافذة التقدم"""
        if self.progress_dialog:
            self.progress_dialog.update_progress(
                message.get('percentage', 0),
                message.get('operation', ''),
                message.get('details', '')
            )

    def _stop_processing(self):
        """إيقاف المعالجة"""
        if self.progress_dialog:
            self.progress_dialog._cancel_operation()

        self._reset_ui_state()
        self._add_status_message("تم إيقاف المعالجة بواسطة المستخدم")

    def _handle_completion(self, results):
        """معالجة اكتمال العملية"""
        self._reset_ui_state()

        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        if results:
            # عرض النتائج
            self.results_viewer.display_results(results)

            # تحديث الحالة
            total_images = results.get('execution_summary', {}).get('total_images_found', 0)
            success_rate = results.get('execution_summary', {}).get('success_rate', 0)

            self.status_var.set(f"اكتملت المعالجة - {total_images} صورة - معدل النجاح: {success_rate:.1f}%")
            self._add_status_message("اكتملت معالجة الصور بنجاح!")

            messagebox.showinfo("نجح", "اكتملت معالجة الصور بنجاح!")
        else:
            self.status_var.set("فشلت المعالجة")
            self._add_status_message("فشلت معالجة الصور")

    def _handle_error(self, error):
        """معالجة الأخطاء"""
        self._reset_ui_state()

        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        self.status_var.set("حدث خطأ في المعالجة")
        self._add_status_message(f"خطأ: {error}")

        messagebox.showerror("خطأ", f"حدث خطأ في المعالجة:\n{error}")

    def _reset_ui_state(self):
        """إعادة تعيين حالة الواجهة"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.current_operation = None

    def _update_status(self, text):
        """تحديث نص الحالة"""
        self.status_var.set(text)

    def _add_status_message(self, message):
        """إضافة رسالة لنص الحالة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, formatted_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    # معالجات القوائم
    def _open_project(self):
        """فتح مشروع محفوظ"""
        file_path = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات المشروع", "*.json"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # تحميل الإعدادات
                self.input_directory.set(project_data.get('input_directory', ''))
                self.output_directory.set(project_data.get('output_directory', ''))
                self.config_file.set(project_data.get('config_file', ''))

                self._add_status_message(f"تم تحميل المشروع: {file_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {e}")

    def _save_project(self):
        """حفظ المشروع الحالي"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات المشروع", "*.json"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                project_data = {
                    'input_directory': self.input_directory.get(),
                    'output_directory': self.output_directory.get(),
                    'config_file': self.config_file.get(),
                    'target_size_width': self.target_size_width.get(),
                    'target_size_height': self.target_size_height.get(),
                    'batch_size': self.batch_size.get(),
                    'enable_analytics': self.enable_analytics.get(),
                    'enable_yolo_export': self.enable_yolo_export.get(),
                    'verbose_mode': self.verbose_mode.get(),
                    'saved_timestamp': datetime.now().isoformat()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, ensure_ascii=False, indent=2)

                self._add_status_message(f"تم حفظ المشروع: {file_path}")
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {e}")

    def _export_results(self):
        """تصدير النتائج"""
        if hasattr(self.results_viewer, 'current_results') and self.results_viewer.current_results:
            self.results_viewer._export_report()
        else:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")

    def _show_analytics(self):
        """عرض التحليلات"""
        messagebox.showinfo("التحليلات", "ميزة التحليلات ستكون متوفرة قريباً")

    def _launch_yolo_training(self):
        """تشغيل تدريب YOLO"""
        try:
            from .training_interface import YOLOTrainingGUI

            # إنشاء نافذة تدريب منفصلة
            training_window = tk.Toplevel(self.root)
            training_window.title("تدريب نماذج YOLO")
            training_window.geometry("800x600")

            # إنشاء واجهة التدريب
            training_gui = YOLOTrainingGUI()
            training_gui.root = training_window
            training_gui._setup_ui()

        except ImportError:
            messagebox.showerror("خطأ", "وحدة تدريب YOLO غير متوفرة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل تدريب YOLO: {e}")

    def _generate_report(self):
        """إنشاء تقرير"""
        messagebox.showinfo("التقرير", "ميزة إنشاء التقارير ستكون متوفرة قريباً")

    def _open_config_editor(self):
        """فتح محرر التكوين"""
        messagebox.showinfo("محرر التكوين", "محرر التكوين سيكون متوفراً قريباً")

    def _open_log_viewer(self):
        """فتح عارض السجلات"""
        messagebox.showinfo("عارض السجلات", "عارض السجلات سيكون متوفراً قريباً")

    def _show_user_guide(self):
        """عرض دليل المستخدم"""
        guide_text = """
دليل المستخدم السريع
===================

1. تحديد المسارات:
   - اختر دليل الصور المدخلة
   - اختر دليل الإخراج
   - (اختياري) حدد ملف التكوين

2. ضبط الإعدادات:
   - حدد الحجم المستهدف للصور
   - اختر حجم الدفعة المناسب
   - فعل الخيارات المطلوبة

3. بدء المعالجة:
   - اضغط على "بدء المعالجة"
   - راقب التقدم في النافذة المنبثقة
   - انتظر اكتمال العملية

4. مراجعة النتائج:
   - تصفح النتائج في تبويب "النتائج والتقارير"
   - صدر التقارير حسب الحاجة
        """

        guide_window = tk.Toplevel(self.root)
        guide_window.title("دليل المستخدم")
        guide_window.geometry("600x400")

        text_widget = scrolledtext.ScrolledText(guide_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, guide_text)
        text_widget.config(state=tk.DISABLED)

    def _show_about(self):
        """عرض معلومات التطبيق"""
        about_text = """
تطبيق الإنقاذ والبحث المتقدم
الإصدار 2.0.0

تطبيق متخصص لمعالجة وتصنيف صور الإنقاذ والبحث
باستخدام تقنيات الذكاء الاصطناعي المتقدمة.

الميزات:
• معالجة متقدمة للصور
• تصنيف ذكي للبيئات
• كشف أهداف الإنقاذ
• تصدير مجموعات بيانات YOLO
• تحليلات تكتيكية شاملة
• واجهة مستخدم محسنة

تم التطوير بواسطة فريق الذكاء الاصطناعي
للإنقاذ والبحث
        """

        messagebox.showinfo("حول التطبيق", about_text)

    def run(self):
        """تشغيل الواجهة الرسومية"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()


def main():
    """الدالة الرئيسية لتشغيل الواجهة"""
    try:
        app = EnhancedRescueGUI()
        app.run()
    except ImportError as e:
        print(f"خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات المطلوبة")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")


if __name__ == "__main__":
    main()